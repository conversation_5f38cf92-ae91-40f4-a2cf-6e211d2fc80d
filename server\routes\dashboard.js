const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Link = require('../models/Link');
const { authenticate } = require('../middleware/auth');
// Dashboard Middleware
const getDashboardData = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    const links = await Link.find({ createdBy: req.user.id })
      .sort('-createdAt')
      .limit(5);
    const totalLinks = await Link.countDocuments({ createdBy: req.user.id });
    const totalClicks = await Link.aggregate([
      { $match: { createdBy: req.user.id } },
      { $group: { _id: null, total: { $sum: "$clicks" } } }
    ]);
    res.locals.user = user;
    res.locals.recentLinks = links;
    res.locals.totalLinks = totalLinks;
    res.locals.totalClicks = totalClicks[0]?.total || 0;
    next();
  } catch (error) {
    console.error(error);
    res.status(500).render('error/500');
  }
};

// Apply middleware to all dashboard routes
router.use(authenticate);
router.use(getDashboardData);

// Dashboard Overview
router.get('/', (req, res) => {
  res.render('dashboard/index', { title: 'Dashboard', activeTab: 'overview' });
});

// My URLs Page
router.get('/urls', async (req, res) => {
  try {
    const links = await Link.find({ createdBy: req.user.id }).sort('-createdAt');
    res.render('dashboard/urls', { title: 'My URLs', activeTab: 'urls', links });
  } catch (error) {
    res.status(500).render('error/500');
  }
});

// URL Creation
router.post('/urls/create', async (req, res) => {
  try {
    const { originalUrl } = req.body;
    const shortCode = Math.random().toString(36).substr(2, 6);
    await Link.create({ originalUrl, shortCode, createdBy: req.user.id });
    res.redirect('/dashboard/urls');
  } catch (error) {
    res.status(500).render('error/500');
  }
});

// Analytics Page
router.get('/analytics', async (req, res) => {
  try {
    const analytics = await Link.aggregate([
      { $match: { createdBy: req.user.id } },
      { $group: { _id: null, totalClicks: { $sum: "$clicks" }, topLinks: { $push: { shortCode: "$shortCode", clicks: "$clicks" } } } }
    ]);

    res.render('dashboard/analytics', {
      title: 'Analytics',
      activeTab: 'analytics',
      totalClicks: analytics[0]?.totalClicks || 0,
      topLinks: analytics[0]?.topLinks || []
    });
  } catch (error) {
    res.status(500).render('error/500');
  }
});

module.exports = router;
