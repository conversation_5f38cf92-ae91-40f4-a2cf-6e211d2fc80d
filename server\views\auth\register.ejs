<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) {
      w[l] = w[l] || []; w[l].push({
          'gtm.start':
              new Date().getTime(), event: 'gtm.js'
      }); var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
              'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
  })(window, document, 'script', 'dataLayer', 'GTM-TXN2ZQQC');</script>
<!-- End Google Tag Manager -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Create an Account</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Cloudflare Turnstile -->
  <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
</head>

<body class="bg-gray-100 flex min-h-screen ">
     <!-- Google Tag Manager (noscript) -->
     <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TXN2ZQQC" height="0" width="0"
      style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
  <!-- Registration Form -->
  <div class="registration-form w-full md:w-1/2 flex items-center justify-center bg-white p-6 md:p-12">
    <div class="w-full max-w-md">
      <!-- Friendly Welcome Message -->
      <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-r-lg shadow-md flex items-center gap-4">
        <a href="/"><img src="/img/urldn-logo-1.webp" alt="Logo" class="w-10 h-10"></a>
        <p class="text-blue-800 font-medium">
          Welcome! We're excited to help you get starte.
        </p>
      </div>

      <%- include('flash-messages.ejs') %>

        <form action="/auth/register" method="POST" class="space-y-6" id="loading">
          <input type="hidden" name="_csrf" value="<%= csrfToken %>">

          <div>
            <label class="block text-gray-700 text-sm font-semibold mb-2" for="email">Email</label>
            <input
              class="w-full px-4 py-3 rounded-lg bg-gray-100 border-transparent focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500 text-gray-700"
              id="email" type="email" name="email" required placeholder="Enter your email address" autocomplete="email">
          </div>

          <div>
            <label class="block text-gray-700 text-sm font-semibold mb-2" for="password">Password</label>
            <div class="relative">
              <input
                class="w-full px-4 py-3 rounded-lg bg-gray-100 border-transparent focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500 text-gray-700 valider-password"
                id="password" type="password" name="password" required placeholder="Create a strong, unique password"
                autocomplete="new-password">
              <button type="button"
                class="password-toggle absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                data-for="password">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
            </div>
          </div>

          <div>
            <label class="block text-gray-700 text-sm font-semibold mb-2" for="confirmPassword">Confirm
              Password</label>
            <div class="relative">
              <input
                class="w-full px-4 py-3 rounded-lg bg-gray-100 border-transparent focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500 text-gray-700 valider-password"
                id="confirmPassword" type="password" name="confirmPassword" required placeholder="Confirm your password"
                autocomplete="new-password">
              <button type="button"
                class="password-toggle absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                data-for="confirmPassword">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
            </div>
          </div>
          <div class="cf-turnstile" data-sitekey="0x4AAAAAAA_DFk3ei4SmzDjq"></div>

          <button
            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold py-3 rounded-lg hover:from-blue-600 hover:to-purple-700 transition duration-300"
            type="submit"
            id="submitBtn">
            <span>Create Free Account</span>
          </button>

        </form>

        <div>
          <p class="text-xs text-gray-600 mt-2  text-center">
            By creating an account, you agree to our
            <a href="/terms" class="text-blue-500 hover:underline">Terms of Service</a> and
            <a href="/privacy" class="text-blue-500 hover:underline">Privacy Policy</a>
          </p>
        </div>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Or continue with</span>
            </div>
          </div>

          <div class="mt-6">
            <a href="/auth/google"
              class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  fill="#4285F4" />
                <path
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  fill="#34A853" />
                <path
                  d="M5.84 14.1c-.22-.66-.35-1.36-.35-2.1s.13-1.44.35-2.1V7.06H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.94l2.85-2.22.81-.62z"
                  fill="#FBBC05" />
                <path
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.06l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  fill="#EA4335" />
              </svg>
              Continue with Google
            </a>
          </div>
        </div>

        <p class="text-center text-gray-600 text-sm mt-6">
          Already have an account?
          <a href="/auth/login" class="text-blue-600 hover:text-blue-800 font-semibold">Log In</a>
        </p>
    </div>
  </div>

  <!-- Right Sidebar-->
  <div
    class="sidebar  hidden md:flex w-1/2 items-center justify-center bg-blue-50 border-l-4 border-blue-500  rounded-r-lg shadow-md ">
    <div class="text-center">
      <img src="/uploads/urldn-register.png" alt="">
    </div>
  </div>

  <%- include('loading.ejs') %>

</body>

</html>