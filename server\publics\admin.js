document.addEventListener('DOMContentLoaded', function () {
    fetchBlogs();
});

document.getElementById('createBlogForm').addEventListener('submit', function (event) {
    event.preventDefault();
    createBlog();
});

// Removed duplicate fetchBlogs function - using the one below with blogsData

let blogsData = [];
let visibleCount = 5; // Number of blogs to show initially

function fetchBlogs() {
    fetch('/api/blogs/blog-admin')
        .then(response => response.json())
        .then(blogs => {
            blogsData = blogs; // Store all blogs
            displayBlogs();
        })
        .catch(error => console.error('Error fetching blogs:', error));
}

function displayBlogs() {
    const blogList = document.getElementById('blogList');
    blogList.innerHTML = '';

    const visibleBlogs = blogsData.slice(0, visibleCount); // Show limited blogs

    visibleBlogs.forEach(blog => {
        const blogItem = document.createElement('div');
        blogItem.classList.add('blog-item');

        const blogImage = document.createElement('img');
        blogImage.src = blog.image;
        blogImage.alt = blog.image;
        blogImage.classList.add('blog-image');

        const blogTitle = document.createElement('h3');
        blogTitle.classList.add('blog-title');
        blogTitle.textContent = blog.title;

        const blogCategory = document.createElement('p');
        blogCategory.classList.add('blog-category');
        blogCategory.textContent = `Category: ${blog.category}`;

        const editButton = document.createElement('button');
        editButton.classList.add('edit-button');
        editButton.textContent = 'Edit';
        editButton.onclick = function () {
            editBlog(blog.slug, blog.title, blog.h1, blog.content, blog.image, blog.category, blog.metaDescription);
        };

        const deleteButton = document.createElement('button');
        deleteButton.classList.add('delete-button');
        deleteButton.textContent = 'Delete';
        deleteButton.onclick = function () {
            deleteBlog(blog.slug);
        };

        blogItem.appendChild(blogImage);
        blogItem.appendChild(blogTitle);
        blogItem.appendChild(blogCategory);
        blogItem.appendChild(editButton);
        blogItem.appendChild(deleteButton);

        blogList.appendChild(blogItem);
    });

    updateShowMoreButton();
}

function updateShowMoreButton() {
    let showMoreButton = document.getElementById('showMoreBtn');
    if (!showMoreButton) {
        showMoreButton = document.createElement('button');
        showMoreButton.id = 'showMoreBtn';
        showMoreButton.classList.add('show-more-button');
        showMoreButton.addEventListener('click', toggleShowMore);
        document.getElementById('blogList').after(showMoreButton);
    }

    if (visibleCount < blogsData.length) {
        showMoreButton.textContent = 'Show More';
    } else {
        showMoreButton.textContent = 'Show Less';
    }
}

function toggleShowMore() {
    if (visibleCount < blogsData.length) {
        visibleCount = blogsData.length; // Show all blogs
    } else {
        visibleCount = 5; // Reset to initial limit
    }
    displayBlogs();
}


function createBlog() {
    const formData = new FormData();
    const title = document.getElementById('title').value;
    const h1 = document.getElementById('h1').value;
    const content = document.getElementById('content').value;
    const image = document.getElementById('image').files[0];
    const category = document.getElementById('category').value;
    const slug = document.getElementById('slug').value;
    const metaDescription = document.getElementById('metaDescription').value;

    // Validation
    if (!title || !h1 || !content || !category) {
        alert('Please fill in all required fields (SEO Title, H1 Heading, Content, Category)');
        return;
    }

    formData.append('title', title);
    formData.append('h1', h1);
    formData.append('content', content);
    formData.append('image', image);
    formData.append('category', category);
    formData.append('slug', slug); // Will be auto-generated if empty
    formData.append('metaDescription', metaDescription);

    fetch('/api/blogs', {
        method: 'POST',
        body: formData
    })
        .then(response => response.json())
        .then(blog => {
            alert('Blog created!');
            fetchBlogs();
            clearForm();
        })
        .catch(error => console.error('Error creating blog:', error));
}

function editBlog(slug, title, h1, content, image, category, metaDescription) {
    document.getElementById('title').value = title || '';
    document.getElementById('h1').value = h1 || title; // Fallback to title if h1 doesn't exist
    document.getElementById('content').value = content || '';
    document.getElementById('slug').value = slug || '';
    document.getElementById('category').value = category || '';
    document.getElementById('metaDescription').value = metaDescription || '';
    document.getElementById('editSlug').value = slug;

    document.getElementById('submitButton').classList.add('hidden');
    document.getElementById('editButton').classList.remove('hidden');
}

function submitEdit() {
    const slug = document.getElementById('editSlug').value;
    const formData = new FormData();
    const title = document.getElementById('title').value;
    const h1 = document.getElementById('h1').value;
    const content = document.getElementById('content').value;
    const image = document.getElementById('image').files[0];
    const category = document.getElementById('category').value;
    const newSlug = document.getElementById('slug').value;
    const metaDescription = document.getElementById('metaDescription').value;

    // Validation
    if (!title || !h1 || !content || !category) {
        alert('Please fill in all required fields (SEO Title, H1 Heading, Content, Category)');
        return;
    }

    formData.append('title', title);
    formData.append('h1', h1);
    formData.append('content', content);
    if (image) {
        formData.append('image', image);
    }
    formData.append('category', category);
    formData.append('slug', newSlug); // New slug if changed
    formData.append('metaDescription', metaDescription);

    fetch(`/api/blogs/${slug}`, {
        method: 'PUT',
        body: formData
    })
        .then(response => response.json())
        .then(updatedBlog => {
            alert('Blog updated!');
            fetchBlogs();
            clearForm();
        })
        .catch(error => console.error('Error updating blog:', error));
}

function deleteBlog(slug) {
    if (confirm('Delete this blog?')) {
        fetch(`/api/blogs/${slug}`, {
            method: 'DELETE'
        })
            .then(() => {
                alert('Blog deleted!');
                fetchBlogs();
            })
            .catch(error => console.error('Error deleting blog:', error));
    }
}

function clearForm() {
    document.getElementById('title').value = '';
    document.getElementById('h1').value = '';
    document.getElementById('content').value = '';
    document.getElementById('editSlug').value = '';
    document.getElementById('image').value = '';
    document.getElementById('category').value = '';
    document.getElementById('slug').value = '';
    document.getElementById('metaDescription').value = '';

    document.getElementById('submitButton').classList.remove('hidden');
    document.getElementById('editButton').classList.add('hidden');
}