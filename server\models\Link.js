// models/Link.js
const mongoose = require('mongoose');

const linkSchema = new mongoose.Schema({
  originalUrl: {
    type: String,
    required: true,
    trim: true,
  },
  shortCode: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 4,
    maxlength: 10,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  expiresAt: {
    type: Date,
    default: null, // Optional: Set expiration date for links
  },
  clicks: {
    type: Number,
    default: 0,
  },
  clickData: [
    {
      timestamp: {
        type: Date,
        default: Date.now,
      },
      ipAddress: String,
      referrer: String,
      userAgent: String,
      country: String,
      deviceType: {
        type: String,
        enum: ['desktop', 'mobile', 'tablet', 'other'],
      },
    },
  ],
  tags: [String], // Optional: For categorizing links
  isActive: {
    type: Boolean,
    default: true,
  },
});

// Indexes for faster queries
linkSchema.index({ shortCode: 1 }, { unique: true });
linkSchema.index({ createdBy: 1 });

module.exports = mongoose.model('Link', linkSchema);