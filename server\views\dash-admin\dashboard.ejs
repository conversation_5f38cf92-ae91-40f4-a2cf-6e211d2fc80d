<%- include('partials/head.ejs') %>

    <body class="bg-gray-50">
        <%- include('partials/nav.ejs') %>

            <!-- Main Content -->
            <div class="ml-0 md:ml-64 pt-16 pb-8 px-4 sm:px-6 lg:px-8 transition-all duration-300" id="content">
                <%- include('partials/flash-messages.ejs') %>
                    <!-- Stats Overview -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mt-4 sm:mt-8">
                        <!-- Total Users Card -->
                        <div class="bg-white rounded-lg shadow p-4 sm:p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-indigo-500 flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z">
                                        </path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-base sm:text-lg font-medium text-gray-900">Total Users</h3>
                                    <p class="text-xl sm:text-2xl font-semibold text-gray-900">
                                        <%= stats.totalUsers %>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Total Links Card -->
                        <div class="bg-white rounded-lg shadow p-4 sm:p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-500 flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1">
                                        </path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-base sm:text-lg font-medium text-gray-900">Total Links</h3>
                                    <p class="text-xl sm:text-2xl font-semibold text-gray-900">
                                        <%= stats.totalLinks %>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <!-- Total Urls anonymous Card -->
                        <div class="bg-white rounded-lg shadow p-4 sm:p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-500 flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1">
                                        </path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-base sm:text-lg font-medium text-gray-900">Total URLs Anonymous</h3>
                                    <p class="text-xl sm:text-2xl font-semibold text-gray-900">
                                        <%= stats.totalAnyUrls %>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Total Posts Card -->
                        <div class="bg-white rounded-lg shadow p-4 sm:p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-500 flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z">
                                        </path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-base sm:text-lg font-medium text-gray-900">Total Posts</h3>
                                    <p class="text-xl sm:text-2xl font-semibold text-gray-900">
                                        <%= stats.totalPosts || 0 %>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Quick Status -->
                    <div class="mt-4 sm:mt-8">
                        <div class="bg-white rounded-lg shadow p-4 sm:p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">
                                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                        </path>
                                    </svg>
                                    SEO Health Status
                                </h3>
                                <a href="/dash-admin/dashboard/seo"
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    View Full Dashboard
                                </a>
                            </div>
                            <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                                <div class="text-center">
                                    <div
                                        class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <p class="text-sm font-medium text-gray-900">Compression</p>
                                    <p class="text-xs text-green-600">Enabled</p>
                                </div>
                                <div class="text-center">
                                    <div
                                        class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <p class="text-sm font-medium text-gray-900">Structured Data</p>
                                    <p class="text-xs text-green-600">Active</p>
                                </div>
                                <div class="text-center">
                                    <div
                                        class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <p class="text-sm font-medium text-gray-900">Hreflang</p>
                                    <p class="text-xs text-green-600">19 Languages</p>
                                </div>
                                <div class="text-center">
                                    <div
                                        class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                            </path>
                                        </svg>
                                    </div>
                                    <p class="text-sm font-medium text-gray-900">Sitemap</p>
                                    <p class="text-xs text-blue-600">Dynamic</p>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-green-50 rounded-lg">
                                <p class="text-sm text-green-800">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    All SEO systems are operational. Last checked: <span id="seo-last-check">Just
                                        now</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 mt-4 sm:mt-8">
                        <!-- Recent Users -->
                        <div class="bg-white shadow rounded-lg">
                            <div class="px-4 py-4 sm:py-5 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Recent Users</h3>
                                <a href="/dash-admin/dashboard/users"
                                    class="text-sm text-indigo-600 hover:text-indigo-800">View All</a>
                            </div>
                            <ul class="divide-y divide-gray-200 max-h-[400px] overflow-y-auto">
                                <% stats.recentUsers.forEach(user=> { %>
                                    <li class="p-3 sm:p-4 flex justify-between items-center">
                                        <div class="min-w-0 flex-1">
                                            <p class="text-sm font-medium text-gray-900 truncate">
                                                <%= user.username %>
                                            </p>
                                            <p class="text-xs sm:text-sm text-gray-500 truncate">
                                                <%= user.email %>
                                            </p>
                                        </div>
                                        <p class="text-xs sm:text-sm text-gray-500 ml-4">
                                            <%= new Date(user.createdAt).toLocaleDateString() %>
                                        </p>
                                    </li>
                                    <% }) %>
                            </ul>
                        </div>

                        <!-- Recent Links -->
                        <div class="bg-white shadow rounded-lg">
                            <div class="px-4 py-4 sm:py-5 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Recent Links</h3>
                                <a href="/dash-admin/dashboard/links"
                                    class="text-sm text-indigo-600 hover:text-indigo-800">View All</a>
                            </div>
                            <ul class="divide-y divide-gray-200 max-h-[400px] overflow-y-auto">
                                <% stats.recentLinks.forEach(link=> { %>
                                    <li class="p-3 sm:p-4">
                                        <div class="flex justify-between items-start">
                                            <p class="text-sm font-medium text-gray-900 truncate flex-1 mr-2">
                                                <%= link.originalUrl %>
                                            </p>
                                            <p class="text-xs sm:text-sm text-gray-500 whitespace-nowrap">
                                                <%= new Date(link.createdAt).toLocaleDateString() %>
                                            </p>
                                        </div>
                                        <div class="flex justify-between items-center mt-2">
                                            <p class="text-xs sm:text-sm text-indigo-600 truncate">/<%= link.shortCode
                                                    %>
                                            </p>
                                            <p class="text-xs sm:text-sm text-gray-500 ml-2">by <%= link.createdBy ?
                                                    link.createdBy.username : 'Unknown' %>
                                            </p>
                                        </div>
                                    </li>
                                    <% }) %>
                            </ul>
                        </div>
                        <!-- Recent URls -->
                        <div class="bg-white shadow rounded-lg">
                            <div class="px-4 py-4 sm:py-5 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Recent Links</h3>
                                <a href="/dash-admin/dashboard/anonymous-urls"
                                    class="text-sm text-indigo-600 hover:text-indigo-800">View All</a>
                            </div>
                            <ul class="divide-y divide-gray-200 max-h-[400px] overflow-y-auto">
                                <% stats.recentUrls.forEach(link=> { %>
                                    <li class="p-3 sm:p-4">
                                        <div class="flex justify-between items-start">
                                            <p class="text-sm font-medium text-gray-900 truncate flex-1 mr-2">
                                                <%= link.longUrl %>
                                            </p>
                                            <p class="text-xs sm:text-sm text-gray-500 whitespace-nowrap">
                                                <%= new Date(link.createdAt).toLocaleDateString() %>
                                            </p>
                                        </div>
                                        <div class="flex justify-between items-center mt-2">
                                            <p class="text-xs sm:text-sm text-indigo-600 truncate">/<%= link.shortId %>
                                            </p>
                                            <p class="text-xs sm:text-sm text-gray-500 ml-2">by anonymous</p>
                                        </div>
                                    </li>
                                    <% }) %>
                            </ul>
                        </div>

                        <!-- Recent Blog Posts -->
                        <div class="bg-white shadow rounded-lg">
                            <div class="px-4 py-4 sm:py-5 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-base sm:text-lg font-medium text-gray-900">Recent Posts</h3>
                                <a href="/dash-admin/dashboard/blogs"
                                    class="text-sm text-indigo-600 hover:text-indigo-800">View All</a>
                            </div>
                            <ul class="divide-y divide-gray-200 max-h-[400px] overflow-y-auto">
                                <% (stats.recentPosts || []).forEach(post=> { %>
                                    <li class="p-3 sm:p-4">
                                        <div class="flex justify-between items-start">
                                            <p class="text-sm font-medium text-gray-900 truncate flex-1 mr-2">
                                                <%= post.title %>
                                            </p>
                                            <span
                                                class="px-2 py-0.5 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800 whitespace-nowrap">
                                                <%= post.category %>
                                            </span>
                                        </div>
                                        <div class="flex justify-between items-center mt-2">
                                            <p class="text-xs sm:text-sm text-gray-500">
                                                <%= new Date(post.createdAt).toLocaleDateString() %>
                                            </p>
                                            <a href="/dash-admin/dashboard/blogs"
                                                class="text-xs sm:text-sm text-indigo-600 hover:text-indigo-800">Edit</a>
                                        </div>
                                    </li>
                                    <% }) %>
                            </ul>
                        </div>
                    </div>
            </div>
    </body>

    </html>