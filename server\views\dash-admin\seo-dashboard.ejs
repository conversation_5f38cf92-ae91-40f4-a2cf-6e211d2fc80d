<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Dashboard - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .seo-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }

        .seo-card:hover {
            transform: translateY(-2px);
        }

        .status-good {
            border-left-color: #28a745;
        }

        .status-warning {
            border-left-color: #ffc107;
        }

        .status-error {
            border-left-color: #dc3545;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .issue-item {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            background-color: #f8f9fa;
        }

        .issue-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .issue-warning {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-chart-line"></i> SEO Dashboard</h1>
                    <div>
                        <a href="/dash-admin/dashboard" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <button class="btn btn-primary" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        <% if (messages.error && messages.error.length> 0) { %>
            <div class="alert alert-danger alert-dismissible fade show">
                <%= messages.error[0] %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <% } %>

                <!-- SEO Overview Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div
                            class="card seo-card <%= seoReport.structuredDataIssues.length === 0 ? 'status-good' : 'status-error' %>">
                            <div class="card-body text-center">
                                <i class="fas fa-code fa-2x mb-2 text-primary"></i>
                                <h5>Structured Data</h5>
                                <div
                                    class="metric-value <%= seoReport.structuredDataIssues.length === 0 ? 'text-success' : 'text-danger' %>">
                                    <%= seoReport.structuredDataIssues.length %>
                                </div>
                                <small class="text-muted">Issues Found</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div
                            class="card seo-card <%= seoReport.duplicateTitles.hasDuplicates ? 'status-error' : 'status-good' %>">
                            <div class="card-body text-center">
                                <i class="fas fa-copy fa-2x mb-2 text-primary"></i>
                                <h5>Duplicate Titles</h5>
                                <div
                                    class="metric-value <%= seoReport.duplicateTitles.hasDuplicates ? 'text-danger' : 'text-success' %>">
                                    <%= seoReport.duplicateTitles.duplicates ?
                                        seoReport.duplicateTitles.duplicates.length : 0 %>
                                </div>
                                <small class="text-muted">Duplicates Found</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card seo-card status-good">
                            <div class="card-body text-center">
                                <i class="fas fa-sitemap fa-2x mb-2 text-primary"></i>
                                <h5>Sitemap URLs</h5>
                                <div class="metric-value text-success">
                                    <%= sitemapHealth.totalURLs %>
                                </div>
                                <small class="text-muted">Total URLs</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card seo-card <%= hreflangValidation.isValid ? 'status-good' : 'status-warning' %>">
                            <div class="card-body text-center">
                                <i class="fas fa-globe fa-2x mb-2 text-primary"></i>
                                <h5>Hreflang</h5>
                                <div
                                    class="metric-value <%= hreflangValidation.isValid ? 'text-success' : 'text-warning' %>">
                                    <%= hreflangValidation.totalLanguages %>
                                </div>
                                <small class="text-muted">Languages</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Reports -->
                <div class="row">
                    <!-- Content Quality Report -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-file-alt"></i> Content Quality Report</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h6>Total Posts</h6>
                                        <span class="badge bg-primary fs-6">
                                            <%= seoReport.totalPosts %>
                                        </span>
                                    </div>
                                    <div class="col-4">
                                        <h6>Avg Words</h6>
                                        <span class="badge bg-info fs-6">
                                            <%= seoReport.averageWordCount %>
                                        </span>
                                    </div>
                                    <div class="col-4">
                                        <h6>With Images</h6>
                                        <span class="badge bg-success fs-6">
                                            <%= seoReport.postsWithImages %>
                                        </span>
                                    </div>
                                </div>
                                <hr>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h6>Short Posts (&lt;300w)</h6>
                                        <span class="badge bg-warning fs-6">
                                            <%= seoReport.shortPosts %>
                                        </span>
                                    </div>
                                    <div class="col-6">
                                        <h6>Long Posts (&gt;2000w)</h6>
                                        <span class="badge bg-secondary fs-6">
                                            <%= seoReport.longPosts %>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-tachometer-alt"></i> Performance Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>Compression Enabled</span>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Yes
                                        </span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>Cache Headers</span>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Yes
                                        </span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>HTTPS Enabled</span>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Yes
                                        </span>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <span>Structured Data</span>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Implemented
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Tools Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-tools"></i> SEO Analysis Tools</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Validate Structured Data -->
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0"><i class="fas fa-code"></i> Validate Structured Data
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <form id="validateStructuredDataForm">
                                                    <div class="mb-3">
                                                        <label for="slugInput" class="form-label">Blog Post Slug</label>
                                                        <input type="text" class="form-control" id="slugInput"
                                                            placeholder="enter-blog-slug-here" required>
                                                        <small class="form-text text-muted">Enter the slug of the blog
                                                            post to validate</small>
                                                    </div>
                                                    <% if (sitemapHealth.recentPosts &&
                                                        sitemapHealth.recentPosts.length> 0) { %>
                                                        <div class="mb-3">
                                                            <label class="form-label">Quick Select (Recent
                                                                Posts)</label>
                                                            <div class="d-flex flex-wrap gap-1">
                                                                <% sitemapHealth.recentPosts.slice(0, 3).forEach(post=>
                                                                    { %>
                                                                    <button type="button"
                                                                        class="btn btn-outline-primary btn-sm"
                                                                        onclick="document.getElementById('slugInput').value='<%= post.slug %>'">
                                                                        <%= post.slug %>
                                                                    </button>
                                                                    <% }) %>
                                                            </div>
                                                        </div>
                                                        <% } %>
                                                            <button type="submit" class="btn btn-primary">
                                                                <i class="fas fa-check-circle"></i> Validate Structured
                                                                Data
                                                            </button>
                                                </form>
                                                <div id="structuredDataResult" class="mt-3" style="display: none;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Check Duplicates -->
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-warning">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0"><i class="fas fa-copy"></i> Check Duplicate Titles</h6>
                                            </div>
                                            <div class="card-body">
                                                <p class="text-muted">Scan all blog posts for duplicate titles</p>
                                                <button type="button" class="btn btn-warning"
                                                    onclick="checkDuplicates()">
                                                    <i class="fas fa-search"></i> Check for Duplicates
                                                </button>
                                                <div id="duplicatesResult" class="mt-3" style="display: none;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Sitemap Health -->
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0"><i class="fas fa-sitemap"></i> Sitemap Health Check
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <p class="text-muted">Check sitemap status and recent updates</p>
                                                <button type="button" class="btn btn-success"
                                                    onclick="checkSitemapHealth()">
                                                    <i class="fas fa-heartbeat"></i> Check Sitemap Health
                                                </button>
                                                <div id="sitemapHealthResult" class="mt-3" style="display: none;"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Generate Full Report -->
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-info">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0"><i class="fas fa-chart-line"></i> Generate SEO Report
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <p class="text-muted">Generate comprehensive SEO analysis report</p>
                                                <button type="button" class="btn btn-info"
                                                    onclick="generateFullReport()">
                                                    <i class="fas fa-file-alt"></i> Generate Report
                                                </button>
                                                <div id="fullReportResult" class="mt-3" style="display: none;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fix Missing Fields Section -->
                <% if (seoReport.structuredDataIssues.length> 0) { %>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="fas fa-tools"></i> Fix Structured Data Issues</h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-exclamation-triangle"></i>
                                            <%= seoReport.structuredDataIssues.length %> Issues Found
                                        </h6>
                                        <p class="mb-2">Most issues are likely missing H1 headings and meta descriptions
                                            in existing blog posts.</p>
                                        <p class="mb-0">Click the button below to automatically fix these issues by:</p>
                                        <ul class="mt-2 mb-0">
                                            <li>Adding H1 headings (using existing titles)</li>
                                            <li>Generating meta descriptions from content</li>
                                            <li>Adding missing featured flags</li>
                                        </ul>
                                    </div>
                                    <button type="button" class="btn btn-danger" onclick="fixMissingFields()">
                                        <i class="fas fa-magic"></i> Auto-Fix Missing SEO Fields
                                    </button>
                                    <div id="fixFieldsResult" class="mt-3" style="display: none;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <% } %>

                        <!-- Issues Section -->
                        <% if (seoReport.structuredDataIssues.length> 0 || (seoReport.duplicateTitles.duplicates &&
                            seoReport.duplicateTitles.duplicates.length > 0)) { %>
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-exclamation-triangle text-warning"></i> Issues
                                                Requiring
                                                Attention</h5>
                                        </div>
                                        <div class="card-body">
                                            <!-- Structured Data Issues -->
                                            <% if (seoReport.structuredDataIssues.length> 0) { %>
                                                <h6>Structured Data Issues:</h6>
                                                <% seoReport.structuredDataIssues.forEach(issue=> { %>
                                                    <div class="issue-item issue-error">
                                                        <strong>
                                                            <%= issue.slug %>
                                                        </strong>: <%= issue.issues.join(', ') %>
                        </div>
                        <% }) %>
                        <% } %>

                        <!-- Duplicate Titles -->
                        <% if (seoReport.duplicateTitles.duplicates && seoReport.duplicateTitles.duplicates.length > 0) { %>
                        <h6 class="mt-3">Duplicate Titles:</h6>
                        <% seoReport.duplicateTitles.duplicates.forEach(duplicate => { %>
                        <div class="issue-item issue-warning">
                            <strong><%= duplicate.title %></strong>: Found in <%= duplicate.slugs.join(' , ') %>
                        </div>
                        <% }) %>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
        <% } %>

        <!-- Recent Blog Posts -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> Recent Blog Posts</h5>
                    </div>
                    <div class="card-body">
                        <% if (sitemapHealth.recentPosts && sitemapHealth.recentPosts.length > 0) { %>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Slug</th>
                                        <th>Last Modified</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% sitemapHealth.recentPosts.forEach(post => { %>
                                    <tr>
                                        <td><%= post.title %></td>
                                        <td><code><%= post.slug %></code></td>
                                        <td><%= new Date(post.lastModified).toLocaleDateString() %></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="validatePost('
                                                            <%=post.slug %>')">
                                                            <i class="fas fa-check"></i> Validate
                                                            </button>
                                                            </td>
                                                            </tr>
                                                            <% }) %>
                                                                </tbody>
                                                                </table>
                                                    </div>
                                                    <% } else { %>
                                                        <p class="text-muted">No recent blog posts found.</p>
                                                        <% } %>
                                        </div>
                                    </div>
                                </div>
                            </div>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="refreshData()" title="Refresh SEO Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Handle structured data validation form
        document.getElementById('validateStructuredDataForm').addEventListener('submit', async function (e) {
            e.preventDefault();
            const slug = document.getElementById('slugInput').value.trim();
            if (!slug) {
                alert('Please enter a blog post slug');
                return;
            }
            await validateStructuredData(slug);
        });

        // Validate structured data for specific slug
        async function validateStructuredData(slug) {
            const resultDiv = document.getElementById('structuredDataResult');
            const btn = document.querySelector('#validateStructuredDataForm button');

            try {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating...';
                resultDiv.style.display = 'none';

                const response = await fetch(`/dash-admin/dashboard/seo/api/validate/${slug}`);
                const result = await response.json();

                if (result.status === 'success') {
                    const data = result.data;
                    let html = '';

                    if (data.isValid) {
                        html = `
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> Validation Successful</h6>
                                <p><strong>Slug:</strong> ${data.slug}</p>
                                <p><strong>Word Count:</strong> ${data.wordCount} words</p>
                                <p class="mb-0">✅ All structured data requirements are met!</p>
                            </div>
                        `;
                    } else {
                        html = `
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Issues Found</h6>
                                <p><strong>Slug:</strong> ${data.slug}</p>
                                <p><strong>Word Count:</strong> ${data.wordCount} words</p>
                                <p><strong>Issues:</strong></p>
                                <ul class="mb-0">
                                    ${data.issues.map(issue => `<li>${issue}</li>`).join('')}
                                </ul>
                            </div>
                        `;
                    }

                    resultDiv.innerHTML = html;
                    resultDiv.style.display = 'block';
                } else {
                    throw new Error(result.message || 'Validation failed');
                }
            } catch (error) {
                console.error('Error validating structured data:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle"></i> Error</h6>
                        <p class="mb-0">Failed to validate structured data: ${error.message}</p>
                    </div>
                `;
                resultDiv.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check-circle"></i> Validate Structured Data';
            }
        }

        // Check for duplicate titles
        async function checkDuplicates() {
            const resultDiv = document.getElementById('duplicatesResult');
            const btn = event.target;

            try {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
                resultDiv.style.display = 'none';

                const response = await fetch('/dash-admin/dashboard/seo/api/duplicates');
                const result = await response.json();

                if (result.status === 'success') {
                    const data = result.data;
                    let html = '';

                    if (!data.hasDuplicates) {
                        html = `
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> No Duplicates Found</h6>
                                <p class="mb-0">✅ All ${data.totalChecked} blog posts have unique titles!</p>
                            </div>
                        `;
                    } else {
                        html = `
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Duplicate Titles Found</h6>
                                <p><strong>Total Posts Checked:</strong> ${data.totalChecked}</p>
                                <p><strong>Duplicates:</strong></p>
                                <ul class="mb-0">
                                    ${data.duplicates.map(dup => `
                                        <li><strong>"${dup.title}"</strong> found in: ${dup.slugs.join(', ')}</li>
                                    `).join('')}
                                </ul>
                            </div>
                        `;
                    }

                    resultDiv.innerHTML = html;
                    resultDiv.style.display = 'block';
                } else {
                    throw new Error(result.message || 'Check failed');
                }
            } catch (error) {
                console.error('Error checking duplicates:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle"></i> Error</h6>
                        <p class="mb-0">Failed to check duplicates: ${error.message}</p>
                    </div>
                `;
                resultDiv.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-search"></i> Check for Duplicates';
            }
        }

        // Check sitemap health
        async function checkSitemapHealth() {
            const resultDiv = document.getElementById('sitemapHealthResult');
            const btn = event.target;

            try {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
                resultDiv.style.display = 'none';

                const response = await fetch('/dash-admin/dashboard/seo/api/sitemap-health');
                const result = await response.json();

                if (result.status === 'success') {
                    const data = result.data;
                    const html = `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-sitemap"></i> Sitemap Health Report</h6>
                            <div class="row">
                                <div class="col-6">
                                    <p><strong>Total URLs:</strong> ${data.totalURLs}</p>
                                    <p><strong>Blog Posts:</strong> ${data.totalBlogPosts}</p>
                                </div>
                                <div class="col-6">
                                    <p><strong>Static Pages:</strong> ${data.totalStaticPages}</p>
                                    <p><strong>Last Updated:</strong> ${new Date(data.lastUpdated).toLocaleString()}</p>
                                </div>
                            </div>
                            ${data.recentPosts && data.recentPosts.length > 0 ? `
                                <p><strong>Recent Posts:</strong></p>
                                <ul class="mb-0">
                                    ${data.recentPosts.map(post => `
                                        <li>${post.title} (${post.slug})</li>
                                    `).join('')}
                                </ul>
                            ` : ''}
                        </div>
                    `;

                    resultDiv.innerHTML = html;
                    resultDiv.style.display = 'block';
                } else {
                    throw new Error(result.message || 'Health check failed');
                }
            } catch (error) {
                console.error('Error checking sitemap health:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle"></i> Error</h6>
                        <p class="mb-0">Failed to check sitemap health: ${error.message}</p>
                    </div>
                `;
                resultDiv.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-heartbeat"></i> Check Sitemap Health';
            }
        }

        // Generate full SEO report
        async function generateFullReport() {
            const resultDiv = document.getElementById('fullReportResult');
            const btn = event.target;

            try {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
                resultDiv.style.display = 'none';

                const response = await fetch('/dash-admin/dashboard/seo/api/report');
                const result = await response.json();

                if (result.status === 'success') {
                    const data = result.data;
                    const html = `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-chart-line"></i> SEO Report Generated</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Total Posts:</strong> ${data.totalPosts}</p>
                                    <p><strong>Published Posts:</strong> ${data.publishedPosts}</p>
                                    <p><strong>Featured Posts:</strong> ${data.featuredPosts}</p>
                                    <p><strong>Posts with Images:</strong> ${data.postsWithImages}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Average Word Count:</strong> ${data.averageWordCount}</p>
                                    <p><strong>Short Posts (&lt;300w):</strong> ${data.shortPosts}</p>
                                    <p><strong>Long Posts (&gt;2000w):</strong> ${data.longPosts}</p>
                                    <p><strong>Posts with Categories:</strong> ${data.postsWithCategories}</p>
                                </div>
                            </div>
                            ${data.structuredDataIssues && data.structuredDataIssues.length > 0 ? `
                                <div class="mt-3">
                                    <p><strong>Structured Data Issues:</strong></p>
                                    <ul class="mb-0">
                                        ${data.structuredDataIssues.map(issue => `
                                            <li><strong>${issue.slug}:</strong> ${issue.issues.join(', ')}</li>
                                        `).join('')}
                                    </ul>
                                </div>
                            ` : '<p class="mt-3 mb-0">✅ No structured data issues found!</p>'}
                        </div>
                    `;

                    resultDiv.innerHTML = html;
                    resultDiv.style.display = 'block';
                } else {
                    throw new Error(result.message || 'Report generation failed');
                }
            } catch (error) {
                console.error('Error generating report:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle"></i> Error</h6>
                        <p class="mb-0">Failed to generate report: ${error.message}</p>
                    </div>
                `;
                resultDiv.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-file-alt"></i> Generate Report';
            }
        }

        async function refreshData() {
            const refreshBtn = document.querySelector('.refresh-btn i');
            refreshBtn.classList.add('fa-spin');

            try {
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } catch (error) {
                console.error('Error refreshing data:', error);
                alert('Error refreshing data. Please try again.');
            } finally {
                refreshBtn.classList.remove('fa-spin');
            }
        }

        // Fix missing SEO fields in existing blog posts
        async function fixMissingFields() {
            const resultDiv = document.getElementById('fixFieldsResult');
            const btn = event.target;

            try {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Fixing...';
                resultDiv.style.display = 'none';

                const response = await fetch('/dash-admin/dashboard/seo/api/fix-missing-fields', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const result = await response.json();

                if (result.status === 'success') {
                    const data = result.data;
                    let html = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> Fields Fixed Successfully!</h6>
                            <div class="row">
                                <div class="col-6">
                                    <p><strong>Total Posts Processed:</strong> ${data.totalProcessed}</p>
                                </div>
                                <div class="col-6">
                                    <p><strong>Posts Fixed:</strong> ${data.fixedCount}</p>
                                </div>
                            </div>
                    `;

                    if (data.results && data.results.length > 0) {
                        html += `
                            <div class="mt-3">
                                <p><strong>Fixed Posts:</strong></p>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Slug</th>
                                                <th>Changes Made</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${data.results.map(post => `
                                                <tr>
                                                    <td>${post.title}</td>
                                                    <td><code>${post.slug}</code></td>
                                                    <td>${post.changes.join(', ')}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        `;
                    }

                    html += `
                            <div class="mt-3">
                                <p class="mb-0">✅ All missing SEO fields have been fixed! Refresh the page to see updated results.</p>
                            </div>
                        </div>
                    `;

                    resultDiv.innerHTML = html;
                    resultDiv.style.display = 'block';

                    // Auto-refresh after 3 seconds
                    setTimeout(() => {
                        location.reload();
                    }, 3000);
                } else {
                    throw new Error(result.message || 'Fix operation failed');
                }
            } catch (error) {
                console.error('Error fixing missing fields:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle"></i> Error</h6>
                        <p class="mb-0">Failed to fix missing fields: ${error.message}</p>
                    </div>
                `;
                resultDiv.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic"></i> Auto-Fix Missing SEO Fields';
            }
        }

        async function validatePost(slug) {
            await validateStructuredData(slug);
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            console.log('Auto-refreshing SEO data...');
            refreshData();
        }, 300000);
    </script>
</body>

</html>