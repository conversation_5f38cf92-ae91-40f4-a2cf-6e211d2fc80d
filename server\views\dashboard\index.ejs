<%- include('partials/head.ejs') %>
    <body class="bg-gray-100">
        <%- include('partials/nav.ejs') %>
            <main class="ml-64  p-6">
                <h1 class="text-3xl font-bold mb-6">Dashboard Overview</h1>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white shadow-md rounded-lg p-6">
                        <h2 class="text-xl font-semibold mb-4">Total Links</h2>
                        <p class="text-3xl font-bold">
                            <%= totalLinks %>
                        </p>
                    </div>
                    <div class="bg-white shadow-md rounded-lg p-6">
                        <h2 class="text-xl font-semibold mb-4">Total Clicks</h2>
                        <p class="text-3xl font-bold">
                            <%= totalClicks %>
                        </p>
                    </div>
                    <div class="bg-white shadow-md rounded-lg p-6">
                        <h2 class="text-xl font-semibold mb-4">Recent Links</h2>
                        <ul class="space-y-4">
                            <% recentLinks.forEach(link=> { %>
                                <li class="flex justify-between">
                                    <span class="text-gray-700">
                                        <%= link.originalUrl %>
                                    </span>
                                    <span class="text-gray-500">/<%= link.shortCode %></span>
                                </li>
                                <% }) %>
                        </ul>
                    </div>
                </div>
            </main>
    </body>

    </html>