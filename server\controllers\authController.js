const User = require('../models/User');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { sendEmail } = require('../utils/email');
const { isDisposableEmail, verifyEmailWithAPI } = require('../utils/emailValidator');
const { verifyTurnstileToken } = require('../utils/turnstileVerification');

// Register a new user
exports.register = async (req, res) => {
  try {
    const body = new URLSearchParams(req.body);
    const token = body.get("cf-turnstile-response");
    const { email, password } = req.body;

    // Verify Turnstile token first
    const isTokenValid = await verifyTurnstileToken(token);
    if (!isTokenValid) {
      req.flash('error', 'Please complete the security verification check');
      return res.redirect('/auth/register');
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      req.flash('error', 'Email already registered');
      return res.redirect('/auth/register');
    }

    // Basic disposable email check first
    if (isDisposableEmail(email)) {
      req.flash('error', 'Please enter a valid email address');
      return res.redirect('/auth/register');
    }

    // Email validation using API
    try {
      const emailValidation = await verifyEmailWithAPI(email);
      if (!emailValidation.isValid) {
        req.flash('error', 'Please enter a valid email address');
        return res.redirect('/auth/register');
      }
    } catch (error) {
      req.flash('error', 'Please enter a valid email address');
      return res.redirect('/auth/register');
    }

    // Create new user
    const user = new User({ email, password });
    user.generateVerificationToken(); // Generate verification token
    await user.save();

    // Send verification email
    const verificationUrl = `${process.env.BASE_URL}/auth/verify-email/${user.verificationToken}`;
    await sendEmail({
      to: user.email,
      subject: 'Welcome to URLDN - Please Verify Your Email',
      template: 'emails/verification-email',
      context: {
        verificationUrl
      }
    });

    req.flash('success', 'Registration successful! Please check your email to verify your account.');
    res.redirect('/auth/login');
  } catch (err) {
    console.error('Registration error:', err);
    req.flash('error', 'An error occurred during registration.');
    res.redirect('/auth/register');
  }
};

// Login user
exports.login = async (req, res) => {
  try {
    const body = new URLSearchParams(req.body);
    const turnstile = body.get("cf-turnstile-response");

    // Verify Turnstile token first
    const isTokenValid = await verifyTurnstileToken(turnstile);
    if (!isTokenValid) {
      req.flash('error', 'Please complete the security verification check');
      return res.redirect('/auth/login');
    }

    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      req.flash('error', 'Invalid email or password.');
      return res.redirect('/auth/login');
    }

    // Check if user is registered with Google
    if (user.googleId) {
      req.flash('error', 'This account uses Google Sign-In. Please login with Google.');
      return res.redirect('/auth/login');
    }

    // Only check password if it's not a Google account
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      req.flash('error', 'Invalid email or password.');
      return res.redirect('/auth/login');
    }

    // Check if user is verified
    if (!user.isVerified) {
      req.flash('error', 'Please verify your email before logging in.');
      return res.redirect('/auth/login');
    }

    // Generate JWT token
    const token = jwt.sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
    res.cookie('jwt', token, {
      httpOnly: true,
      SameSite: "strict",
      secure: process.env.COOKIE_SECURE === 'true', // Use secure cookies based on environment
    });
    res.redirect('/dashboard');
  } catch (err) {
    console.error('Login error:', err);
    req.flash('error', 'An error occurred during login.');
    res.redirect('/auth/login');
  }
};

// Verify email
exports.verifyEmail = async (req, res) => {
  const { token } = req.params;

  try {
    const user = await User.findOne({
      verificationToken: token,
      verificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      req.flash('error', 'Invalid or expired verification token.');
      return res.redirect('/auth/register');
    }

    user.isVerified = true;
    user.verificationToken = undefined;
    user.verificationExpires = undefined;
    await user.save();

    res.render('auth/email-verified'); // Render the email verified page
  } catch (err) {
    console.error('Verification error:', err);
    req.flash('error', 'An error occurred during email verification.');
    res.redirect('/auth/register');
  }
};

// Forgot password method
exports.forgotPassword = async (req, res) => {
  try {
    const body = new URLSearchParams(req.body);
    const token = body.get("cf-turnstile-response");
    const { email } = req.body;

    // Verify Turnstile token first
    const isTokenValid = await verifyTurnstileToken(token);
    if (!isTokenValid) {
      req.flash('error', 'Please complete the security verification check');
      return res.redirect('/auth/forgot-password');
    }

    const user = await User.findOne({ email });

    if (!user) {
      req.flash('error', 'No user found with this email.');
      return res.redirect('/auth/forgot-password');
    }

    // Check if user is registered with Google
    if (user.googleId) {
      req.flash('error', 'This email is registered with Google. Please login using Google instead.');
      return res.redirect('/auth/login');
    }

    user.generatePasswordReset(); // Generate password reset token
    await user.save();

    const resetUrl = `${process.env.BASE_URL}/auth/reset-password/${user.resetPasswordToken}`;
    await sendEmail({
      to: user.email,
      subject: 'Reset Your URLDN Password',
      template: 'emails/reset-password',
      context: {
        resetUrl
      }
    });

    req.flash('success', 'Password reset instructions sent to your email.');
    res.redirect('/auth/login');
  } catch (err) {
    console.error('Forgot password error:', err);
    req.flash('error', 'An error occurred during password reset.');
    res.redirect('/auth/forgot-password');
  }
};

// Reset password method
exports.resetPassword = async (req, res) => {
  const { token: resetToken } = req.params;
  try {
    const body = new URLSearchParams(req.body);
    const token = body.get("cf-turnstile-response");
    const { password } = req.body;

    // Verify Turnstile token first
    const isTokenValid = await verifyTurnstileToken(token);
    if (!isTokenValid) {
      req.flash('error', 'Please complete the security verification check');
      return res.redirect(`/auth/reset-password/${resetToken}`);
    }

    const user = await User.findOne({
      resetPasswordToken: resetToken,
      resetPasswordExpires: { $gt: Date.now() }
    });

    if (!user) {
      req.flash('error', 'Invalid or expired password reset token.');
      return res.redirect('/auth/forgot-password');
    }

    user.password = password;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    req.flash('success', 'Password reset successful! Please login with your new password.');
    res.redirect('/auth/login');
  } catch (err) {
    console.error('Reset password error:', err);
    req.flash('error', 'An error occurred during password reset.');
    res.redirect(`/auth/reset-password/${resetToken}`);
  }
};

// Logout user
exports.logout = (req, res) => {
  res.clearCookie('jwt');
  res.redirect('/auth/login');
};