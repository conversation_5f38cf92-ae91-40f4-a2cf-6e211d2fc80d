const express = require('express');
const router = express.Router();
const { shortenUrlController, redirectController, expandUrlController } = require('../controllers/urlController');
const rateLimit = require('express-rate-limit');
// Rate limiter
const shortenerLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10 // limit each IP to 100 requests per windowMs
});
// URL shortening route with rate limiting
router.post('/shorten', shortenerLimiter, shortenUrlController);
// URL expansion route with rate limiting
router.post('/expand', shortenerLimiter, expandUrlController);
// URL redirection route
router.get('/:shortId', redirectController);

module.exports = router;