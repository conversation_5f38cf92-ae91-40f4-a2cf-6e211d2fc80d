const { config } = require('dotenv');
const mongoose = require('mongoose');

if (process.env.NODE_ENV === 'production') {
    config({ path: '.env.production' });
} else {
    config({ path: '.env.development' });
}

function connect() {
    mongoose.connect(process.env.MONGO_URI)
    .then(() => {
        console.log("Successfully connected to MongoDB.");
    })
    .catch((error) => {
        console.error("Error connecting to MongoDB:", error.message);
        process.exit(1);
    });
}

module.exports = { connect };
