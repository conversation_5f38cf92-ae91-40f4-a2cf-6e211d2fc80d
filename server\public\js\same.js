document.addEventListener('DOMContentLoaded',()=>{const menuIcon=document.getElementById('menuIcon');const navLinks=document.getElementById('navLinks');menuIcon.addEventListener('click',()=>{menuIcon.classList.toggle('open');navLinks.classList.toggle('open')});document.addEventListener('click',(event)=>{if(!menuIcon.contains(event.target)&&!navLinks.contains(event.target)){menuIcon.classList.remove('open');navLinks.classList.remove('open')}})});document.querySelectorAll('.faq-question').forEach(question=>{question.addEventListener('click',()=>{question.parentElement.classList.toggle('active')})});const scrollToTopButton=document.querySelector('#scrollTopBtn');window.addEventListener('scroll',()=>{if(window.scrollY>100){scrollToTopButton.classList.add('show')}else{scrollToTopButton.classList.remove('show')}});scrollToTopButton.addEventListener('click',scrollToTop);scrollToTopButton.addEventListener('touchstart',scrollToTop);function scrollToTop(){window.scrollTo({top:0,behavior:'smooth'})}
const currentYear=new Date().getFullYear();document.getElementById('current-year').textContent=currentYear