const Blog = require('../models/blogModel');

exports.generateSitemap = async (req, res) => {
    try {
        const baseUrl = 'https://urldn.com';

        // Static URLs with optional SEO info
        const staticPages = [
            { loc: "/", changefreq: "weekly", priority: "1.0" },
            { loc: "/about", changefreq: "monthly", priority: "0.8" },
            { loc: "/contact", changefreq: "monthly", priority: "0.8" },
            { loc: "/tracking", changefreq: "monthly", priority: "0.8" },
            { loc: "/tools/", changefreq: "monthly", priority: "0.8" },
            { loc: "/tools/utm-builder", changefreq: "monthly", priority: "0.8" },
            { loc: "/tools/bulk-shortener", changefreq: "monthly", priority: "0.8" },
            { loc: "/tools/link-expander", changefreq: "monthly", priority: "0.7" },
            { loc: "/qr-code-generator", changefreq: "monthly", priority: "0.7" },
            { loc: "/faq", changefreq: "monthly", priority: "0.6" },
            { loc: "/privacy", changefreq: "yearly", priority: "0.3" },
            { loc: "/terms", changefreq: "yearly", priority: "0.3" },
            { loc: "/blog", changefreq: "weekly", priority: "0.9" },
            { loc: "/free/url-shortener", changefreq: "weekly", priority: "0.9" },
            { loc: "/blog/author/urldn", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/analytics", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/howto", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/influencers", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/marketing", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/qrcodes", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/tools", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/urldn", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog/category/urldn?page=2", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog?page=2", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog?page=3", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog?page=4", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog?page=5", changefreq: "monthly", priority: "0.6" },
            { loc: "/blog?page=6", changefreq: "monthly", priority: "0.6" },
            // Language pages - all with trailing slashes for SEO consistency
            "/fr/", "/pt/", "/cn/", "/ru/", "/id/", "/th/", "/in/",
            "/ph/", "/tr/", "/de/", "/kr/", "/jp/", "/tw/", "/es/", "/au/", "/vn/", "/my/"
        ].map(p => typeof p === 'string' ? { loc: p, changefreq: "monthly", priority: "0.5" } : p);

        let sitemap = `<?xml version="1.0" encoding="UTF-8"?>\n`;
        sitemap += `<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n`;

        // Add static pages
        staticPages.forEach(page => {
            sitemap += `  <url>\n`;
            sitemap += `    <loc>${baseUrl}${page.loc}</loc>\n`;
            sitemap += `    <changefreq>${page.changefreq}</changefreq>\n`;
            sitemap += `    <priority>${page.priority}</priority>\n`;
            sitemap += `  </url>\n`;
        });

        // Fetch and add blogs
        const blogs = await Blog.find().sort({ updatedAt: -1 });

        if (blogs.length > 0) {
            blogs.forEach(blog => {
                const lastmod = blog.updatedAt?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0];
                sitemap += `  <url>\n`;
                sitemap += `    <loc>${baseUrl}/blog/${blog.slug}</loc>\n`;
                sitemap += `    <lastmod>${lastmod}</lastmod>\n`;
                sitemap += `    <changefreq>weekly</changefreq>\n`;
                sitemap += `    <priority>0.8</priority>\n`;
                sitemap += `  </url>\n`;
            });
        }

        sitemap += `</urlset>`;

        res.header('Content-Type', 'application/xml');
        res.send(sitemap);
    } catch (error) {
        console.error('❌ Error generating sitemap:', error);
        res.status(500).send('Error generating sitemap');
    }
};
