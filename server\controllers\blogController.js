const Blog = require('../models/blogModel');
const slugify = require('slugify');

async function submitUrlsToIndexNow(urls, searchEngineEndpoint) {
    const headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Host": new URL(searchEngineEndpoint).hostname,
    };
    const payload = {
        host: "urldn.com",
        key: "d3ee253c13f84b3f846923f6c64719f5",
        keyLocation: "https://urldn.com/d3ee253c13f84b3f846923f6c64719f5.txt",
        urlList: urls,
    };

    try {
        const response = await fetch(searchEngineEndpoint, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            throw new Error(`Failed to submit URLs to ${searchEngineEndpoint}. Status code: ${response.status}`);
        }
    } catch (error) {
        throw new Error(`Error submitting URLs to ${searchEngineEndpoint}: ${error.message}`);
    }
}

// Function to fetch latest blog URLs
async function getLatestBlogUrls() {
    try {
        // Fetch the latest blog posts (e.g., last 10 posts)
        const latestBlogs = await Blog.find({})
            .sort({ createdAt: -1 }) // Sort by latest first
            .limit(1); // Adjust the limit as needed

        // Generate full URLs for each blog post
        const baseUrl = "https://urldn.com/blog"; // Replace with your blog's base URL
        const urls = latestBlogs.map(blog => `${baseUrl}/${blog.slug}`);
        return urls;
    } catch (error) {
        throw new Error("Error fetching latest blog URLs: " + error.message);
    }
}
// Create a new blog post
exports.createBlog = async (req, res) => {
    const { title, h1, content, category, slug: customSlug, metaDescription } = req.body; // Extract request body
    const image = req.file ? req.file.path : ''; // Get image URL from Cloudinary

    // Use custom slug if provided, otherwise generate from h1 or title
    const slug = customSlug ? slugify(customSlug, { lower: true }) :
        h1 ? slugify(h1, { lower: true }) :
            slugify(title, { lower: true });

    try {
        const newBlog = new Blog({
            title,
            h1: h1 || title, // Use h1 if provided, fallback to title
            content,
            category,
            image, // Save the Cloudinary image URL
            slug,
            metaDescription: metaDescription || content.replace(/<[^>]*>/g, '').trim().substring(0, 160) + '...',
            featured: false // Default to not featured
        });
        await newBlog.save(); // Save to the database
        res.status(201).json(newBlog); // Send the newly created blog as a response
        // After creating the blog, submit the latest URLs to IndexNow
        const latestUrls = await getLatestBlogUrls();

        if (latestUrls.length > 0) {
            // Submit to Yandex
            await submitUrlsToIndexNow(latestUrls, "https://yandex.com/indexnow");

            // Submit to Bing
            await submitUrlsToIndexNow(latestUrls, "https://www.bing.com/indexnow");
        } else {
            console.log("No new blog URLs to submit.");
        }
    } catch (error) {
        res.status(500).json({ error: 'Failed to create blog post' });
    }
};

// Get all blog posts
exports.getBlogs = async (req, res) => {
    try {
        const blogs = await Blog.find(); // Fetch all blog posts
        res.status(200).json(blogs); // Send them as a responsezz
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch blog posts' });
    }
};

// Get blog posts by category
exports.getCategory = async (req, res) => {
    try {
        const blogs = await Blog.find({ category: req.params.category }); // Fetch by category
        if (!blogs.length) {
            return res.status(404).json({ error: 'No blogs found for this category' });
        }
        res.status(200).json(blogs);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch blogs by category' });
    }
};

// Get a specific blog post by slug
exports.getBlogBySlug = async (req, res) => {
    try {
        const blog = await Blog.findOne({ slug: req.params.slug }); // Fetch by slug
        if (!blog) {
            return res.status(404).json({ error: 'Blog not found' });
        }
        res.status(200).json(blog);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to fetch blog post' });
    }
};

// Update a blog post by slug
exports.updateBlogBySlug = async (req, res) => {
    const { slug } = req.params;
    const { title, h1, content, category, slug: customSlug, metaDescription } = req.body;
    const image = req.file ? req.file.path : ''; // Get updated image URL from Cloudinary

    // Use custom slug if provided, otherwise generate from h1 or title
    const newSlug = customSlug ? slugify(customSlug, { lower: true }) :
        h1 ? slugify(h1, { lower: true }) :
            slugify(title, { lower: true });

    try {
        const blog = await Blog.findOne({ slug });

        if (!blog) {
            return res.status(404).json({ error: 'Blog not found' });
        }

        blog.title = title;
        blog.h1 = h1 || title; // Use h1 if provided, fallback to title
        blog.content = content;
        blog.category = category;
        blog.metaDescription = metaDescription || content.replace(/<[^>]*>/g, '').trim().substring(0, 160) + '...';

        if (image) {
            blog.image = image; // Update image if a new one is uploaded
        }
        blog.slug = newSlug;

        await blog.save(); // Save updates to the database
        res.status(200).json(blog);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to update blog post' });
    }
};

// Delete a blog post by slug
exports.deleteBlogBySlug = async (req, res) => {
    const { slug } = req.params;

    try {
        const deletedBlog = await Blog.findOneAndDelete({ slug });

        if (!deletedBlog) {
            return res.status(404).json({ error: 'Blog not found' });
        }

        res.status(200).json({ message: 'Blog deleted successfully' });
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: 'Failed to delete blog post' });
    }
};
