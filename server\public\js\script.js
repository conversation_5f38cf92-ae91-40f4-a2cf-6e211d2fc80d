async function handleSubmit(e){e.preventDefault();const t=document.getElementById("longUrl").value;const n=document.getElementById("loadingIndicator");const o=document.querySelector('button[type="submit"]');document.getElementById("longUrl").value="";if(!isValidUrl(t)){showAlert("Invalid URL format. Please enter a valid URL.","error");return}
if(!document.querySelector('[name="cf-turnstile-response"]').value){showAlert("Please complete the reCAPTCHA.","error");return}
try{n.style.display="block";o.disabled=!0;const e=new Promise(e=>setTimeout(e,800));const a=fetch("/shorten",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({longUrl:t})});const[r]=await Promise.all([a,e]);if(!r.ok){throw new Error("Failed to shorten URL")}
const l=await r.json();showAlert("URL shortened successfully!","success");displayUrls(t,l.shortUrl)}catch(e){showAlert("You have exceeded the URL shortening limit. Please try again.","error")}finally{n.style.display="none";o.disabled=!1}}
function displayUrls(e,t){const n=document.getElementById("resultContainer");n.innerHTML="";const o=document.createElement("div");o.className="saved-url-item";const a=document.createElement("div");a.className="url-display";const r=document.createElement("p");r.textContent="Original URL: ";const l=document.createElement("a");l.href=e;l.textContent=e.substring(0,30)+"...";l.target="_blank";l.rel="noopener noreferrer";r.appendChild(l);a.appendChild(r);const d=createCopyButton(e);a.appendChild(d);o.appendChild(a);const c=document.createElement("div");c.className="url-display";const s=document.createElement("p");s.textContent="Shortened URL: ";const i=document.createElement("a");i.href=t;i.textContent=t;i.target="_blank";i.rel="noopener noreferrer";s.appendChild(i);c.appendChild(s);const p=createCopyButton(t);c.appendChild(p);o.appendChild(c);const m=document.createElement("div");m.className="tracking";const u=document.createElement("a");u.href="/tracking";u.target="_blank";u.rel="noopener noreferrer";const h=document.createElement("i");h.className="fas fa-chart-line tracking-icon";u.appendChild(h);const C=document.createTextNode("STATS");u.appendChild(C);m.appendChild(u);o.appendChild(m);n.appendChild(o);const g=document.createElement("div");const y=document.createElement("a");y.href=`/qr-code-generator?url=${encodeURIComponent(t)}`;y.target="_blank";y.rel="noopener noreferrer";g.className="qr-code";const qrImg=document.createElement("img");qrImg.src="/img/URLdnQrcode.png";qrImg.alt="QR Code";qrImg.width=58;qrImg.height=58;y.appendChild(qrImg);o.appendChild(y);n.appendChild(o);saveUrl(e,t)}
function createCopyButton(e){const t=document.createElement("button");const n=document.createElement("i");n.className="fas fa-copy";t.appendChild(n);t.className="copy-btn";t.addEventListener("click",()=>copyToClipboard(e));return t}
function copyToClipboard(e){navigator.clipboard.writeText(e).then(()=>{showAlert("Copied to clipboard!","success")}).catch(e=>{showAlert("Failed to copy to clipboard","error")})}
function isValidUrl(e){return/^(http|https):\/\/[^ "]+$/.test(e)}
function showAlert(e,t){Toastify({text:e,duration:5000,close:!0,gravity:"top",position:"right",backgroundColor:t==="success"?"green":"red",stopOnFocus:!0}).showToast()}
function saveUrl(e,t){const n=JSON.parse(localStorage.getItem("savedUrls")||"[]");n.push({longUrl:e,shortUrl:t,qrCodeDataUrl:"/img/URLdnQrcode.png"});localStorage.setItem("savedUrls",JSON.stringify(n))}
function loadSavedUrls(){const e=JSON.parse(localStorage.getItem("savedUrls")||"[]");const t=document.getElementById("savedUrlList");t.innerHTML="";if(e.length>0){const e=document.createElement("h2");document.querySelector(".clear-btn").style.display="block";e.textContent="Saved URLs";e.style.textAlign="center";t.appendChild(e)}else{clearBtn.style.display="none"}
e.forEach(({longUrl:e,shortUrl:n})=>{const a=document.createElement("div");a.className="saved-url-item";const r=document.createElement("div");r.className="url-display";const l=document.createElement("p");l.textContent="Original URL: ";const d=document.createElement("a");d.href=e;d.textContent=e.substring(0,30)+"...";d.target="_blank";l.appendChild(d);r.appendChild(l);r.appendChild(d);const c=createCopyButton(e);r.appendChild(c);a.appendChild(r);const s=document.createElement("div");s.className="url-display";const i=document.createElement("p");i.textContent="Shortened URL: ";const p=document.createElement("a");p.href=n;p.textContent=n;p.target="_blank";i.appendChild(p);s.appendChild(i);s.appendChild(p);const m=createCopyButton(n);s.appendChild(m);a.appendChild(s);const u=document.createElement("div");u.className="qr-code";const qrImg=document.createElement("img");qrImg.src="/img/URLdnQrcode.png";qrImg.alt="QR Code";qrImg.width=58;qrImg.height=58;u.appendChild(qrImg);const h=document.createElement("a");h.href=`/qr-code-generator?url=${encodeURIComponent(n)}`;h.target="_blank";h.appendChild(u);a.appendChild(h);t.appendChild(a)})}
function clearSavedUrls(){if(confirm("This action will remove URLs from your local storage, but they will still be available in our database for tracking purposes. Are you sure you want to proceed?")){localStorage.removeItem("savedUrls");loadSavedUrls();showAlert("Saved URLs cleared","success")}}
document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("urlForm");const t=document.getElementById("clearButton");e.addEventListener("submit",handleSubmit);t.addEventListener("click",clearSavedUrls);loadSavedUrls()})