// routes/seoRoutes.js
const express = require('express');
const router = express.Router();
const {
    generateSEOReport,
    checkDuplicateTitles,
    validateStructuredData,
    checkSitemapHealth,
    validateHreflang,
    checkPerformanceMetrics
} = require('../utils/seoMonitor');

// SEO Dashboard - Main health check
router.get('/dashboard', async (req, res) => {
    try {
        const [
            seoReport,
            sitemapHealth,
            hreflangValidation,
            performanceMetrics
        ] = await Promise.all([
            generateSEOReport(),
            checkSitemapHealth(),
            validateHreflang(),
            checkPerformanceMetrics()
        ]);

        res.json({
            status: 'success',
            timestamp: new Date().toISOString(),
            data: {
                seoReport,
                sitemapHealth,
                hreflangValidation,
                performanceMetrics
            }
        });
    } catch (error) {
        console.error('Error generating SEO dashboard:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to generate SEO dashboard',
            error: error.message
        });
    }
});

// Check for duplicate titles
router.get('/check-duplicates', async (req, res) => {
    try {
        const result = await checkDuplicateTitles();
        res.json({
            status: 'success',
            data: result
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Failed to check duplicate titles',
            error: error.message
        });
    }
});

// Validate structured data for a specific blog post
router.get('/validate-structured-data/:slug', async (req, res) => {
    try {
        const { slug } = req.params;
        const result = await validateStructuredData(slug);
        res.json({
            status: 'success',
            data: result
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Failed to validate structured data',
            error: error.message
        });
    }
});

// Sitemap health check
router.get('/sitemap-health', async (req, res) => {
    try {
        const result = await checkSitemapHealth();
        res.json({
            status: 'success',
            data: result
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Failed to check sitemap health',
            error: error.message
        });
    }
});

// Hreflang validation
router.get('/validate-hreflang', (req, res) => {
    try {
        const result = validateHreflang();
        res.json({
            status: 'success',
            data: result
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Failed to validate hreflang',
            error: error.message
        });
    }
});

// Performance metrics
router.get('/performance-metrics', (req, res) => {
    try {
        const result = checkPerformanceMetrics();
        res.json({
            status: 'success',
            data: result
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Failed to check performance metrics',
            error: error.message
        });
    }
});

// Generate comprehensive SEO report
router.get('/report', async (req, res) => {
    try {
        const report = await generateSEOReport();
        res.json({
            status: 'success',
            timestamp: new Date().toISOString(),
            data: report
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Failed to generate SEO report',
            error: error.message
        });
    }
});

module.exports = router;
