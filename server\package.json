{"name": "url-shortener", "version": "1.0.0", "description": "A URL shortener application using Node.js, Express, JS, and MongoDB.", "main": "app.js", "scripts": {"start": "nodemon app.js"}, "dependencies": {"axios": "^1.7.9", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cloudinary": "^1.41.3", "compression": "^1.7.4", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.2.2", "dompurify": "^3.2.3", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.21.2", "express-flash": "^0.0.2", "express-rate-limit": "^7.4.0", "express-session": "^1.18.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "https-proxy-agent": "^7.0.6", "joi": "^17.13.3", "jsdom": "^25.0.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.12.0", "mongoose": "^8.9.5", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nanoid": "^3.1.20", "node-fetch": "^3.3.2", "nodemailer": "^6.9.14", "nodemon": "^3.1.4", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "path": "^0.12.7", "slugify": "^1.6.6", "xss-clean": "^0.1.4"}}