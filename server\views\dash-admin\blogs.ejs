<%- include('partials/head.ejs') %>

    <body class="bg-gray-50">
        <%- include('partials/nav.ejs') %>
            <div class="flex-1 lg:ml-64 py-14 px-12" id="admin-panel">
                <form id="createBlogForm" class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4"
                    enctype="multipart/form-data">
                    <input type="hidden" id="editSlug">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="image">
                            Image
                        </label>
                        <input type="file" id="image"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">

                        <label class="block text-gray-700 text-sm font-bold mb-2" for="title">
                            SEO Title (for &lt;title&gt; tag)
                        </label>
                        <input type="text" id="title" placeholder="SEO Title for search engines"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <small class="text-gray-600">This will be used in the &lt;title&gt; tag and search results
                            (50-60 characters recommended)</small>

                        <label class="block text-gray-700 text-sm font-bold mb-2 mt-4" for="h1">
                            H1 Heading (for page content)
                        </label>
                        <input type="text" id="h1" placeholder="Main heading for the article"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <small class="text-gray-600">This will be the main heading visible on the page (different from
                            SEO title)</small>

                        <label class="block text-gray-700 text-sm font-bold mb-2 mt-4" for="slug">
                            URL Slug
                        </label>
                        <input type="text" id="slug" placeholder="url-friendly-slug"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <small class="text-gray-600">Custom URL slug (will auto-generate from H1 if left empty)</small>

                        <label class="block text-gray-700 text-sm font-bold mb-2 mt-4" for="metaDescription">
                            Meta Description (Optional)
                        </label>
                        <textarea id="metaDescription"
                            placeholder="Brief description for search engines (150-160 characters)"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline h-20"></textarea>
                        <small class="text-gray-600">Custom meta description for search results</small>

                        <label class="block text-gray-700 text-sm font-bold mb-2" for="category">
                            Category
                        </label>
                        <select id="category"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="" disabled selected>Select a category</option>
                            <option value="analytics">Analytics</option>
                            <option value="howto">How To</option>
                            <option value="influencers">Influencers</option>
                            <option value="marketing">Marketing</option>
                            <option value="urldn">urldn</option>
                            <option value="qrcodes">Qr Codes</option>
                            <option value="tools">Tools</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="content">
                            Content
                        </label>
                        <textarea id="content" placeholder="Content"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"></textarea>
                    </div>
                    <div class="flex items-center justify-between">
                        <button type="submit" id="submitButton"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Submit
                        </button>
                        <button type="button" id="editButton"
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline hidden"
                            onclick="submitEdit()">
                            Edit
                        </button>
                    </div>
                </form>
                <div id="blogList" class="mt-8">
                    <!-- Blog posts will be inserted here -->
                </div>
            </div>
            <script src="/admin.js"></script>
    </body>

    </html>