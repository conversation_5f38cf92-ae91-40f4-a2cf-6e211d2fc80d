<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        <%= typeof authorName !=='undefined' ? `${authorName} - ` : '' %>Articles
    </title>
    <meta name="description"
        content="<%='Discover articles by ' + authorName + ', a free URL shortening tool. Explore how to manage long links effectively and learn more about our services.' %>">
    <meta name="author" content="<%= authorName %>">
    <meta property="og:author" content="<%= authorName %>">
    <meta name="twitter:creator" content="@<%= authorName %>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <link rel="stylesheet" href="/styles/style.css">
    <script src="/js/same.js" defer></script>
    <meta name="keywords" content="URL shortening, URLdn, articles, QR codes, analytics">
    <meta name="author" content="<%= authorName %>">
    <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": "URLldn's Articles ",
          "description": "URLdn is a free & easy-to-use URL shortening tool designed for anyone looking to make long links more manageable. With urldn, you can quickly create short, easy-to-share links. With just a few clicks, and generate custom QR codes at no extra cost, perfect for marketing, social media, or everyday use.",
          "image": "https://urldn.com/img/urldn-logo-1.webp",
          "author": {
            "@type": "Person",
            "name": "Urldn",
            "url": "https://urldn.com/blog/author/urldn",
            "sameAs": [
              "https://twitter.com/@urldn",
              "https://www.linkedin.com/company/urldn"
            ]
          },
          "publisher": {
            "@type": "Organization",
            "name": "Urldn Blog",
            "logo": {
              "@type": "ImageObject",
              "url": "https://urldn.com/img/urldn-logo-1.webp"
            }
          },
          "datePublished": "2024-12-21T10:00:00Z",
          "dateModified": "2024-12-21T10:00:00Z",
          "mainEntityOfPage": "https://urldn.com/blog/author/urldn"
        }
        </script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8521338629167059"
        crossorigin="anonymous"></script>
</head>

<body>
    <%- include('./partials/header') %>
        <a href="https://buymeacoffee.com/urldn" target="_blank" class="coffee-button">
            <img src="https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png" alt="Buy Me a Coffee" />
        </a>
        <div class="container mt-4">
            <h1 class="text-center mt-5 mb-4" style="font-size: 3rem; font-weight: bold; color: #2ecc71;">

                <%= authorName ? authorName + "'s  Articles" : "Author's" %>
            </h1>
            <hr>
            <section class="about-me text-center">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p>
                            URLdn is a free & easy-to-use URL shortening tool designed for anyone looking to make long
                            links
                            more manageable. With urldn, you can quickly create short, easy-to-share links. With just a
                            few
                            clicks, and generate custom QR codes at no extra cost, perfect for marketing, social media,
                            or
                            everyday use.
                            <hr> urldn also includes built-in analytics, allowing you to track clicks and engagement to
                            understand your link’s performance better. Whether you're a business, influencer, or casual
                            user, urldn provides an all-in-one solution for creating short links, custom QR codes, and
                            tracking analytics—all for free.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h3 class="text-center mb-4">About the Author</h3>
                        <%- include('partials/social-media') %>
                    </div>
                </div>
            </section>
            <h2 class="text-center mt-5 mb-4" style="font-size: 2rem;">More from urldn</h2>

            <% if (!blogs || blogs.length===0) { %>
                <p class="alert alert-info">No blogs found for this author.</p>
                <% } else { %>
                    <div class="row">
                        <% blogs.slice(0, 12).forEach(blog=> { %>
                            <div class="col-md-4 mb-4">
                                <a href="/blog/<%= blog.slug %>" class="text-decoration-none">
                                    <div class="card h-100 card-hover">
                                        <div class="card-body d-flex flex-column"
                                            style="background-color: #f8f9fa; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                            <img src="<%= blog.image %>" class="card-img-top" alt="<%= blog.title %>"
                                                style="height: 200px; object-fit: cover;">
                                            <h5 class="card-title text-center"
                                                style="color: #2c3e50; font-family: 'Arial', sans-serif; font-weight: bold;">
                                                <%= blog.title %>
                                            </h5>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <% }); %>
                    </div>
                    <% } %>
        </div>
        <span id="scrollTopBtn" onclick="scrollToTop()"><i class="fas fa-chevron-up"></i></span>
        <%- include('./partials/footer') %>
</body>

</html>