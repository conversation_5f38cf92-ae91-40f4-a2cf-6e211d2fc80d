<%- include('partials/head.ejs') %>

    <body class="bg-gray-100">
        <%- include('partials/nav.ejs') %>

            <div class="md:pl-64 p-12">
                <%- include('partials/flash-messages.ejs') %>
                    <div class="max-w-7xl mx-auto py-4 sm:py-6 sm:px-6 lg:px-8">
                        <div class="bg-white shadow rounded-lg">
                            <div class="px-4 py-5 border-b border-gray-200">
                                <h3 class="text-lg font-medium leading-6 text-gray-900">Manage Links</h3>
                            </div>

                            <!-- Links Table -->
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Short URL</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Original URL</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Created By</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Created At</th>
                                            <th
                                                class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <% links.forEach(link=> { %>
                                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <a href="/<%= link.shortCode %>" target="_blank"
                                                        class="text-sm font-medium text-blue-600">
                                                        /<%= link.shortCode %>
                                                    </a>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-500">
                                                        <%= link.originalUrl %>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-500">
                                                        <%= link.createdBy ? link.createdBy.username ||
                                                            link.createdBy.email : 'Anonymous' %>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-500">
                                                        <%= new Date(link.createdAt).toLocaleDateString() %>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-right">
                                                    <form action="/dash-admin/dashboard/links/<%= link._id %>/delete"
                                                        method="POST" class="inline">
                                                        <button type="submit"
                                                            class="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 transition-colors duration-150"
                                                            onclick="return confirm('Are you sure you want to delete this link?')">
                                                            Delete
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                            <% }) %>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="px-4 py-4 sm:px-6 border-t border-gray-200">
                                <nav class="flex items-center justify-between">
                                    <div class="hidden sm:flex sm:items-center sm:justify-between w-full">
                                        <div>
                                            <p class="text-sm text-gray-700">
                                                Showing page <span class="font-medium">
                                                    <%= currentPage %>
                                                </span> of
                                                <span class="font-medium">
                                                    <%= totalPages %>
                                                </span>
                                            </p>
                                        </div>
                                        <div class="flex space-x-2">
                                            <% if (currentPage> 1) { %>
                                                <a href="/dash-admin/dashboard/links?page=<%= currentPage - 1 %>&limit=10"
                                                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-150">
                                                    Previous
                                                </a>
                                                <% } %>
                                                    <% if (currentPage < totalPages) { %>
                                                        <a href="/dash-admin/dashboard/links?page=<%= currentPage + 1 %>&limit=10"
                                                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-150">
                                                            Next
                                                        </a>
                                                        <% } %>
                                        </div>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>
            </div>
    </body>

    </html>