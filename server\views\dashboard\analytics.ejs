<%- include('partials/head.ejs') %>
    <body class="bg-gray-100">
        <%- include('partials/nav.ejs') %>
            <main class="ml-64  p-6">
                <h1 class="text-3xl font-bold mb-6">Analytics</h1>
                <div class="bg-white shadow-md rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Total Clicks: <%= totalClicks %>
                    </h2>

                    <h3 class="text-lg font-semibold mb-4">Top Links</h3>
                    <ul class="space-y-4">
                        <% topLinks.forEach(link=> { %>
                            <li class="flex justify-between">
                                <span class="text-gray-700">/<%= link.shortCode %></span>
                                <span class="text-gray-500">Clicks: <%= link.clicks %></span>
                            </li>
                            <% }) %>
                    </ul>
                </div>
            </main>

    </body>

    </html>