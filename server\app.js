// app.js
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const hpp = require('hpp');
const xssClean = require('xss-clean');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const flash = require('express-flash');
const passport = require('passport');
const compression = require('compression');
const app = express();
const PORT = process.env.PORT || 5000;
// Database connection
const { connect } = require('./config/db');
connect();

//  app.use(
//   helmet.contentSecurityPolicy({
//     directives: {
//       defaultSrc: ["'self'"],
//       scriptSrc: [
//         "'self'",
//         "'unsafe-inline'", 
//         "'unsafe-eval'",   
//         "https://cdnjs.cloudflare.com",
//         "https://www.googletagmanager.com",
//         "https://www.google.com",
//         "https://cdn.jsdelivr.net",
//         "https://code.jquery.com",
//         "https://challenges.cloudflare.com",
//         "https://cdn.emailjs.com",
//         "https://cdn.tailwindcss.com",


//         "https://pagead2.googlesyndication.com",
//         "https://googleads.g.doubleclick.net",
//           "https://www.clarity.ms"  
//       ],
//       connectSrc: [
//         "'self'",
//         "https://www.googletagmanager.com",
//         "https://challenges.cloudflare.com",
//         "https://cdn.emailjs.com",
//         "https://api.emailjs.com"
//       ],
//       frameSrc: [
//         "'self'",
//         "https://www.googletagmanager.com",
//         "https://challenges.cloudflare.com",
//         "https://www.youtube.com",
//         "https://www.youtube-nocookie.com",


//         "https://googleads.g.doubleclick.net",
//         "https://tpc.googlesyndication.com"
//       ],
//       imgSrc: [
//         "'self'",
//         "https://res.cloudinary.com",
//         "https://cdn.buymeacoffee.com",
//         "https://flagcdn.com",
//         "https://www.google-analytics.com",
//         "data:",

//         "https://pagead2.googlesyndication.com",
//         "https://googleads.g.doubleclick.net",
//         "https://tpc.googlesyndication.com"
//       ],
//       mediaSrc: ["'self'"],
//       childSrc: [
//         "'self'",
//         "https://www.youtube.com"
//       ]
//     }
//   })
// ); 

// Enable compression for all responses
app.use(compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      // Don't compress responses with this request header
      return false;
    }
    // Fallback to standard filter function
    return compression.filter(req, res);
  },
  level: 6, // Compression level (1-9, 6 is default)
  threshold: 1024, // Only compress responses larger than 1KB
}));

app.use(
  helmet.hsts({
    maxAge: 60 * 60 * 24 * 365, // 1 year
    includeSubDomains: true,    // Apply HSTS to subdomains
    preload: true               // Preload the domain into browsers
  })
);
app.use(cors());
app.use(bodyParser.json());
app.use(cookieParser());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(hpp());
app.use(xssClean());

// Session middleware
app.use(
  session({
    secret: process.env.SESSION_SECRET, // Replace with a real secret key
    resave: false,
    saveUninitialized: true,
    cookie: { SameSite: "Lax", maxAge: 1000 * 60 * 60 * 24 }
  })
);
// Flash middleware
app.use(flash());
// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());
// Require the passport configuration file
require('./config/passport'); // Ensure this file exists and is properly configured
// Routes
const urlRoutes = require('./routes/urlRoutes');
const userRouteClicks = require('./routes/userRouteClicks');
const dashboardRoutes = require('./routes/dashboard');
const authRoutes = require('./routes/authRoutes');
const sitemapRoutes = require('./routes/sitemapRoutes');
const blogsSsr = require('./routes/SSR');
const toolsRouter = require('./routes/toolsRouter');
// SEO routes are now integrated into protected admin dashboard

app.use(sitemapRoutes);
app.use(express.static('public'));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/blog/uploads', express.static('uploads'));
app.use('/blog/author/uploads', express.static('uploads'));
app.use('/blog/category/uploads', express.static('uploads'));

// Serve static files
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(express.static(path.join(__dirname, './public')));
app.use(express.static(path.join(__dirname, 'publics')));

// Static routes
app.get('/free/url-shortener', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'free-url-shortener', 'index.html'));
});
app.get('/about', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'pages', 'about.html'));
});
app.get('/contact', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'pages', 'contact.html'));
});
app.get('/faq', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'pages', 'faq.html'));
});
app.get('/privacy', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'pages', 'privacy.html'));
});
app.get('/terms', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'pages', 'terms.html'));
});
app.get('/qr-code-generator', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'pages', 'qr-code-generator.html'));
});
app.get('/tracking', (req, res) => {
  res.sendFile(path.join(__dirname, './public', 'pages', 'tracking.html'));
});

// Auth routes
app.use('/auth', authRoutes);

// Tools routes
app.use('/tools', toolsRouter);

// SEO monitoring is now integrated into the protected admin dashboard

// Other routes
app.get('/blog', require('./routes/blog'));
app.use('/dash-admin', require('./routes/admin'));
app.use('/dash-admin/dashboard', require('./routes/dashboard-admin'));
app.use('/dashboard', dashboardRoutes);
app.use('/', urlRoutes);
app.use('/api', userRouteClicks);
app.use('/', blogsSsr);
app.use('/api/blogs', require('./routes/blogRoutes'));

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
