<%- include('partials/head.ejs') %>

    <body class="bg-gray-50">
        <%- include('partials/nav.ejs') %>
            <div class="flex-1 lg:ml-64 py-14 px-12 ">
                <div class="bg-white rounded-lg shadow mt-8">
                    <%- include('partials/flash-messages.ejs') %>
                        <div class="overflow-x-auto">
                            <table class="min-w-full border border-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                            Short URL</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Long
                                            URL</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                            Clicks</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                            Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <% urls.forEach(url=> { %>
                                        <tr class="text-sm">
                                            <td class="px-4 py-2 whitespace-nowrap">
                                                <div class="truncate max-w-[100px] md:max-w-xs">
                                                    <%= url.shortId %>
                                                </div>
                                            </td>
                                            <td class="px-4 py-2">
                                                <span
                                                    class="text-blue-600 hover:text-blue-800 block truncate max-w-[150px] md:max-w-full">
                                                    <%= url.longUrl.substr(0, 50) + (url.longUrl.length> 50 ? '...' :
                                                        '') %>
                                                </span>
                                            </td>
                                            <td class="px-4 py-2 text-center">
                                                <%= url.clickCount %>
                                            </td>
                                            <td class="px-4 py-2">
                                                <form
                                                    action="/dash-admin/dashboard/anonymous-urls/<%= url.shortId %>/delete"
                                                    method="POST">
                                                    <button type="submit"
                                                        class="px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 transition duration-150"
                                                        onclick="return confirm('Are you sure you want to delete this URL?')">
                                                        Delete
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <% }) %>
                                </tbody>
                            </table>
                        </div>

                </div>

                <!-- Pagination Info -->
                <div class="flex items-center justify-between my-4 px-6">
                    <div class="text-sm text-gray-700">
                        Page <%= currentPage %> of <%= totalPages || 1 %>
                                (<%= totalUrls %> total URLs)
                    </div>

                    <!-- Pagination Controls -->
                    <div class="flex justify-center space-x-2">
                        <!-- Previous Button -->
                        <% if (currentPage> 1) { %>
                            <a href="<%= currentPage - 1 === 1 ? '/dash-admin/dashboard/anonymous-urls' : `/dash-admin/dashboard/anonymous-urls?page=${currentPage - 1}` %>"
                                class="px-3 py-2 rounded bg-white text-blue-600 hover:bg-blue-50">
                                Previous
                            </a>
                            <% } else { %>
                                <span class="px-3 py-2 rounded bg-gray-100 text-gray-400">Previous</span>
                                <% } %>

                                    <!-- Page Numbers -->
                                    <% const startPage=Math.max(1, currentPage - 2); const endPage=Math.min(totalPages,
                                        startPage + 4); for (let i=startPage; i <=endPage; i++) { %>
                                        <% if (i===currentPage) { %>
                                            <span class="px-3 py-2 rounded bg-blue-600 text-white">
                                                <%= i %>
                                            </span>
                                            <% } else { %>
                                                <a href="<%= i === 1 ? '/dash-admin/dashboard/anonymous-urls' : `/dash-admin/dashboard/anonymous-urls?page=${i}` %>"
                                                    class="px-3 py-2 rounded bg-white text-blue-600 hover:bg-blue-50">
                                                    <%= i %>
                                                </a>
                                                <% } %>
                                                    <% } %>

                                                        <!-- Next Button -->
                                                        <% if (currentPage < totalPages) { %>
                                                            <a href="/dash-admin/dashboard/anonymous-urls?page=<%= currentPage + 1 %>"
                                                                class="px-3 py-2 rounded bg-white text-blue-600 hover:bg-blue-50">
                                                                Next
                                                            </a>
                                                            <% } else { %>
                                                                <span
                                                                    class="px-3 py-2 rounded bg-gray-100 text-gray-400">Next</span>
                                                                <% } %>
                    </div>
                </div>
            </div>

    </body>

    </html>