<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-TXN2ZQQC');</script>
    <!-- End Google Tag Manager -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Page Title -->
    <title>
        <%= currentCategory.charAt(0).toUpperCase() + currentCategory.slice(1) %> - Insights on URL Shortening
    </title>
    <!-- Meta Description -->
    <meta name="description"
        content="Explore expert insights on <%= currentCategory %>, URL shortening techniques, and effective digital marketing strategies to boost your online presence.">
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<%= currentCategory %> - Insights on URL Shortening">
    <meta property="og:description"
        content="Explore insights and articles about <%= currentCategory %> in the world of URL shortening and digital marketing.">
     <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="index,follow" />
    <meta property="og:image" content="https://urldn.com/img/urldn-logo-1.webp">
    <meta property="og:url"
        content="<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}` %>">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="en_US">
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<%= currentCategory %> - Insights on URL Shortening">
    <meta name="twitter:description"
        content="Explore insights and articles about <%= currentCategory %> in the world of URL shortening and digital marketing.">
    <meta name="twitter:image" content="https://urldn.com/img/urldn-logo-1.webp">
    <!-- Canonical Link -->
    <link rel="canonical"
        href="<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}${currentPage > 1 ? `?page=${currentPage}` : ''}` %>">
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/styles/style.css">
    <link rel="stylesheet" href="/styles/blog.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Scripts -->
    <script src="/js/same.js" defer></script>
    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "CollectionPage",
                "@id": "<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}` %>",
                "url": "<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}` %>",
                "name": "<%= currentCategory.charAt(0).toUpperCase() + currentCategory.slice(1) %> - Insights on URL Shortening",
                "description": "Explore insights and articles about <%= currentCategory %> in the world of URL shortening and digital marketing.",
                "isPartOf": {"@id": "https://urldn.com/blog/#website"},
                "about": {"@id": "https://urldn.com/blog/#organization"},
                "datePublished": "2024-01-01T00:00:00+00:00",
                "dateModified": "2025-01-21T00:00:00+00:00",
                "image": "https://urldn.com/img/urldn-logo-1.webp",
                "breadcrumb": {"@id": "<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}` %>#breadcrumb"},
                "inLanguage": "en-US",
                "potentialAction": [{"@type": "ReadAction", "target": ["<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}` %>"]}]
            },
            {
                "@type": "BreadcrumbList",
                "@id": "<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}` %>#breadcrumb",
                "itemListElement": [
                    {
                        "@type": "ListItem",
                        "position": 1,
                        "name": "Home",
                        "item": "https://urldn.com/"
                    },
                    {
                        "@type": "ListItem",
                        "position": 2,
                        "name": "Blog",
                        "item": "https://urldn.com/blog"
                    },
                    {
                        "@type": "ListItem",
                        "position": 3,
                        "name": "<%= currentCategory.charAt(0).toUpperCase() + currentCategory.slice(1) %>",
                        "item": "<%= `https://urldn.com/blog/category/${currentCategory.toLowerCase().replace(/ /g, '-')}` %>"
                    }
                ]
            },
            {
                "@type": "WebSite",
                "@id": "https://urldn.com/blog/#website",
                "url": "https://urldn.com/blog/",
                "name": "URLDN | Blog",
                "description": "Welcome to the official URLDN blog, covering tips, insights, and updates about URL shortening and link management.",
                "publisher": {"@id": "https://urldn.com/blog/#organization"},
                "inLanguage": "en-US"
            },
            {
                "@type": "Organization",
                "@id": "https://urldn.com/blog/#organization",
                "name": "URLDN",
                "url": "https://urldn.com/",
                "logo": {
                    "@type": "ImageObject",
                    "inLanguage": "en-US",
                    "@id": "https://urldn.com/blog/#/schema/logo/image/",
                    "url": "https://urldn.com/img/urldn-logo-1.webp",
                    "contentUrl": "https://urldn.com/img/urldn-logo-1.webp",
                    "width": 280,
                    "height": 280,
                    "caption": "URLDN"
                },
                "image": {"@id": "https://urldn.com/blog/#/schema/logo/image/"},
                "sameAs": [
                    "https://x.com/urldn_",
                    "https://www.instagram.com/urldn/",
                    "https://www.linkedin.com/company/urldn/",
                    "https://www.youtube.com/channel/urldn"
                ]
            }
        ]
    }
    </script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8521338629167059"
    crossorigin="anonymous"></script>
</head>

<body>

    <%- include('./partials/header') %>
        <main class="track-qr-text caterory-text ">
            <h1 id="category-name">
                <%= currentCategory %>
            </h1> Enhance Your Life with Modern Insights, Ideas, and Inspiration
        </main>
        <div class="breadcrumb-container">
            <nav aria-label="Breadcrumb">
                <ul class="breadcrumb">
                    <li><a href="/"><i class="fas fa-home"></i>Home</a></li>
                    <li><a href="/blog"><i class="fas fa-blog"></i>Blog</a></li>
                    <li><i class="fas fa-folder"></i>Category</li>
                    <li class="active"><i class=" fas fa-file-alt"></i><span id="breadcrumb-category">
                            <%= currentCategory %>
                        </span></li>
                </ul>
            </nav>
        </div>
        <main class="container">
            <div id="more-caterory-container">
                <% blogs.forEach(blog=> { %>
                    <article class="article-home">
                        <a href="/blog/<%= blog.slug %>">
                            <img src="<%= blog.image %>" alt="<%= blog.title %>" width="1200" height="630"
                                loading="lazy" decoding="async" class="blog-thumbnail" />
                            <h2 class="caterory-title">
                                <%= blog.title %>
                            </h2>
                            <p class="blog-content" role="article">
                                <%- blog.content %>

                            </p>
                        </a>
                    </article>
                    <% }) %>
            </div>
        </main>
        <div id="pagination">
            <!-- Previous Button -->
            <% if (currentPage> 1) { %>
                <a href="<%= currentPage - 1 === 1 ? `/blog/category/${currentCategory}` : `/blog/category/${currentCategory}?page=${currentPage - 1}` %>"
                    id="prev-page" class="pagination-link">
                    Previous
                </a>
                <% } else { %>
                    <span class="pagination-disabled">Previous</span>
                    <% } %>

                        <!-- First Page -->
                        <% if (currentPage> 3) { %>
                            <a href="/blog/category/<%= currentCategory %>" class="pagination-link">1</a>
                            <% if (currentPage> 4) { %>
                                <span class="ellipsis">...</span>
                                <% } %>
                                    <% } %>

                                        <!-- Page Links -->
                                        <% for (let i=Math.max(1, currentPage - 2); i <=Math.min(totalPages, currentPage
                                            + 2); i++) { %>
                                            <% if (i===currentPage) { %>
                                                <span class="pagination-current">
                                                    <%= i %>
                                                </span>
                                                <% } else { %>
                                                    <a href="<%= i === 1 ? `/blog/category/${currentCategory}` : `/blog/category/${currentCategory}?page=${i}` %>"
                                                        class="pagination-link">
                                                        <%= i %>
                                                    </a>
                                                    <% } %>
                                                        <% } %>

                                                            <!-- Last Page -->
                                                            <% if (currentPage < totalPages - 2) { %>
                                                                <% if (currentPage < totalPages - 3) { %>
                                                                    <span class="ellipsis">...</span>
                                                                    <% } %>
                                                                        <a href="/blog/category/<%= currentCategory %>?page=<%= totalPages %>"
                                                                            class="pagination-link">
                                                                            <%= totalPages %>
                                                                        </a>
                                                                        <% } %>

                                                                            <!-- Next Button -->
                                                                            <% if (currentPage < totalPages) { %>
                                                                                <a href="/blog/category/<%= currentCategory %>?page=<%= currentPage + 1 %>"
                                                                                    id="next-page"
                                                                                    class="pagination-link">
                                                                                    Next
                                                                                </a>
                                                                                <% } else { %>
                                                                                    <span
                                                                                        class="pagination-disabled">Next</span>
                                                                                    <% } %>
        </div>
          
        <%- include('./partials/footer') %>

            <span id="scrollTopBtn" onclick="scrollToTop()"><i class="fas fa-chevron-up"></i></span>

</body>

</html>
