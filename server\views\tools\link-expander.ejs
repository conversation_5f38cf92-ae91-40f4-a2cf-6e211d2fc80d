<!DOCTYPE html>
<html lang="en">

<head>
    <%- include('../partials/seo', { title, description, url }) %>

        <script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": ["WebApplication", "SoftwareApplication"],
    "name": "Link Expander Tool",
    "description": "Expand shortened URLs to see the original destination before clicking. Check where short links lead with our free link expander tool.",
    "url": "https://urldn.com/tools/link-expander",
    "applicationCategory": "Security Tool",
    "operatingSystem": "Web Browser",
    "requirements": "Requires JavaScript enabled in web browser",
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
    },
    "featureList": [
        "Expand shortened URLs safely",
        "Preview destination before clicking",
        "Security protection against malicious links",
        "Support for all URL shortener formats",
        "Click statistics viewing",
        "Phishing protection"
    ],
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.9",
        "reviewCount": "32"
    },
    "publisher": {
        "@type": "Organization",
        "name": "URLdn",
        "url": "https://urldn.com",
        "logo": {
            "@type": "ImageObject",
            "url": "https://urldn.com/img/urldn-logo-1.webp"
        }
    },
    "breadcrumbList": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://urldn.com"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Tools",
                "item": "https://urldn.com/tools"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "Link Expander",
                "item": "https://urldn.com/tools/link-expander"
            }
        ]
    }
}
</script>

        <style>
            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #374151;
            }

            .form-input {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #e5e7eb;
                border-radius: 0.5rem;
                font-size: 1rem;
                transition: border-color 0.2s;
            }

            .form-input:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .result-box {
                background: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 0.5rem;
                padding: 1.5rem;
                margin-top: 1rem;
            }

            .result-box.success {
                border-color: #10b981;
                background: #ecfdf5;
            }

            .result-box.error {
                border-color: #ef4444;
                background: #fef2f2;
            }

            .url-display {
                word-break: break-all;
                font-family: 'Courier New', monospace;
                background: white;
                padding: 0.75rem;
                border-radius: 0.375rem;
                border: 1px solid #d1d5db;
                margin: 0.5rem 0;
            }

            .copy-btn {
                background: #10b981;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 0.375rem;
                cursor: pointer;
                margin-top: 0.5rem;
                transition: background-color 0.2s;
                font-size: 0.875rem;
            }

            .copy-btn:hover {
                background: #059669;
            }

            .copy-btn.copied {
                background: #6b7280;
            }

            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0;
                border-bottom: 1px solid #e5e7eb;
            }

            .info-item:last-child {
                border-bottom: none;
            }

            .info-label {
                font-weight: 600;
                color: #374151;
            }

            .info-value {
                color: #6b7280;
                font-family: 'Courier New', monospace;
            }

            .loading {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            .warning-box {
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 0.5rem;
                padding: 1rem;
                margin: 1rem 0;
            }

            .warning-box h3 {
                color: #92400e;
                margin: 0 0 0.5rem 0;
                font-size: 1rem;
                font-weight: 600;
            }

            .warning-box p {
                color: #92400e;
                margin: 0;
                font-size: 0.875rem;
            }
        </style>
</head>

<body>

    <%- include('../partials/header') %>

        <main class="container mx-auto px-4 py-8">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm text-gray-600">
                    <li><a href="/" class="hover:text-blue-600">Home</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li><a href="/tools" class="hover:text-blue-600">Tools</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li class="text-gray-800 font-medium">Link Expander</li>
                </ol>
            </nav>

            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                    Link <span class="text-purple-600">Expander</span>
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Expand shortened URLs to see the original destination before clicking.
                    Stay safe by checking where links lead with our free link expander tool.
                </p>
            </div>

            <!-- Link Expander Form -->
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
                    <div class="form-group">
                        <label for="shortUrl" class="form-label">
                            Enter Short URL
                        </label>
                        <input type="text" id="shortUrl" class="form-input"
                            placeholder="https://urldn.com/abc123 or just abc123">
                        <p class="text-sm text-gray-500 mt-1">
                            Enter the full short URL or just the short code
                        </p>
                    </div>

                    <!-- Expand Button -->
                    <div class="text-center">
                        <button onclick="expandUrl()" id="expandBtn"
                            class="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors duration-200 font-semibold">
                            <i class="fas fa-expand-alt mr-2"></i>Expand Link
                        </button>
                    </div>

                    <!-- Warning Box -->
                    <div class="warning-box">
                        <h3><i class="fas fa-exclamation-triangle mr-2"></i>Safety Notice</h3>
                        <p>Always verify the expanded URL before visiting. Be cautious of suspicious or unfamiliar
                            domains.</p>
                    </div>

                    <!-- Result Section -->
                    <div id="resultSection" style="display: none;">
                        <h3 class="text-lg font-semibold mb-4">Expanded URL Information:</h3>
                        <div id="resultBox" class="result-box">
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>

                <!-- Info Section -->
                <div class="mt-12 bg-purple-50 rounded-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Why Use Link Expander?</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="font-semibold mb-2">Security Benefits:</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li>• Check destination before clicking</li>
                                <li>• Avoid malicious or spam websites</li>
                                <li>• Verify legitimate business links</li>
                                <li>• Protect against phishing attempts</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">Supported Formats:</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li>• Full URLs: https://urldn.com/abc123</li>
                                <li>• Short codes: abc123</li>
                                <li>• Any urldn.com shortened link</li>
                                <li>• View click statistics</li>
                            </ul>
                        </div>


                    </div>
                    <!-- Related Tools Section -->
                    <div class="mt-16 text-center">
                        <h2 class="text-3xl font-bold text-gray-800 mb-6">Related Tools</h2>
                        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
                            <a href="/tools/utm-builder"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">UTM Builder</h3>
                                <p class="text-sm text-gray-600">Create tracking URLs</p>
                            </a>

                            <a href="/tools/bulk-shortener"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-compress-alt text-green-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">Bulk Shortener</h3>
                                <p class="text-sm text-gray-600">Shorten multiple URLs</p>
                            </a>

                            <a href="/tracking"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-orange-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-chart-bar text-orange-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">URL Tracking</h3>
                                <p class="text-sm text-gray-600">Track URL performance</p>
                            </a>

                            <a href="/qr-code-generator"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-indigo-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-qrcode text-indigo-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">QR Code Generator</h3>
                                <p class="text-sm text-gray-600">Create QR codes</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </main>
        <span id="scrollTopBtn" onclick="scrollToTop()"><i class="fas fa-chevron-up"></i></span>

        <%- include('../partials/footer') %>

            <script>
                async function expandUrl() {
                    const shortUrl = document.getElementById('shortUrl').value.trim();

                    if (!shortUrl) {
                        alert('Please enter a short URL');
                        return;
                    }

                    const btn = document.getElementById('expandBtn');
                    const resultSection = document.getElementById('resultSection');
                    const resultBox = document.getElementById('resultBox');
                    const resultContent = document.getElementById('resultContent');

                    // Show loading state
                    btn.disabled = true;
                    btn.innerHTML = '<div class="loading"></div> Expanding...';

                    try {
                        const response = await fetch('/expand', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ shortUrl: shortUrl })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            // Success - show expanded URL info
                            resultBox.className = 'result-box success';

                            // Clear previous content
                            resultContent.innerHTML = '';

                            // Create Short URL info item
                            const shortUrlItem = document.createElement('div');
                            shortUrlItem.className = 'info-item';

                            const shortUrlLabel = document.createElement('span');
                            shortUrlLabel.className = 'info-label';
                            shortUrlLabel.textContent = 'Short URL:';

                            const shortUrlValue = document.createElement('span');
                            shortUrlValue.className = 'info-value';
                            shortUrlValue.textContent = data.shortUrl;

                            shortUrlItem.appendChild(shortUrlLabel);
                            shortUrlItem.appendChild(shortUrlValue);

                            // Create Expanded URL info item
                            const expandedUrlItem = document.createElement('div');
                            expandedUrlItem.className = 'info-item';

                            const expandedUrlLabel = document.createElement('span');
                            expandedUrlLabel.className = 'info-label';
                            expandedUrlLabel.textContent = 'Expanded URL:';

                            const expandedUrlContainer = document.createElement('div');

                            const urlDisplay = document.createElement('div');
                            urlDisplay.className = 'url-display';
                            urlDisplay.textContent = data.longUrl;

                            const copyBtn = document.createElement('button');
                            copyBtn.className = 'copy-btn';
                            copyBtn.onclick = () => copyUrl(data.longUrl);

                            const copyIcon = document.createElement('i');
                            copyIcon.className = 'fas fa-copy mr-1';
                            copyBtn.appendChild(copyIcon);
                            copyBtn.appendChild(document.createTextNode('Copy URL'));

                            const visitBtn = document.createElement('a');
                            visitBtn.href = data.longUrl;
                            visitBtn.target = '_blank';
                            visitBtn.rel = 'noopener noreferrer';
                            visitBtn.className = 'copy-btn';
                            visitBtn.style.background = '#3b82f6';
                            visitBtn.style.marginLeft = '0.5rem';
                            visitBtn.style.textDecoration = 'none';

                            const visitIcon = document.createElement('i');
                            visitIcon.className = 'fas fa-external-link-alt mr-1';
                            visitBtn.appendChild(visitIcon);
                            visitBtn.appendChild(document.createTextNode('Visit Site'));

                            expandedUrlContainer.appendChild(urlDisplay);
                            expandedUrlContainer.appendChild(copyBtn);
                            expandedUrlContainer.appendChild(visitBtn);

                            expandedUrlItem.appendChild(expandedUrlLabel);
                            expandedUrlItem.appendChild(expandedUrlContainer);

                            // Create Total Clicks info item
                            const clicksItem = document.createElement('div');
                            clicksItem.className = 'info-item';

                            const clicksLabel = document.createElement('span');
                            clicksLabel.className = 'info-label';
                            clicksLabel.textContent = 'Total Clicks:';

                            const clicksValue = document.createElement('span');
                            clicksValue.className = 'info-value';
                            clicksValue.textContent = data.clickCount;

                            clicksItem.appendChild(clicksLabel);
                            clicksItem.appendChild(clicksValue);

                            // Create Short Code info item
                            const codeItem = document.createElement('div');
                            codeItem.className = 'info-item';

                            const codeLabel = document.createElement('span');
                            codeLabel.className = 'info-label';
                            codeLabel.textContent = 'Short Code:';

                            const codeValue = document.createElement('span');
                            codeValue.className = 'info-value';
                            codeValue.textContent = data.shortId;

                            codeItem.appendChild(codeLabel);
                            codeItem.appendChild(codeValue);

                            // Append all items to result content
                            resultContent.appendChild(shortUrlItem);
                            resultContent.appendChild(expandedUrlItem);
                            resultContent.appendChild(clicksItem);
                            resultContent.appendChild(codeItem);
                        } else {
                            // Error - show error message
                            resultBox.className = 'result-box error';

                            // Clear previous content
                            resultContent.innerHTML = '';

                            const errorContainer = document.createElement('div');
                            errorContainer.style.textAlign = 'center';

                            const errorIcon = document.createElement('i');
                            errorIcon.className = 'fas fa-exclamation-circle text-red-500 text-2xl mb-2';

                            const errorTitle = document.createElement('h4');
                            errorTitle.style.color = '#dc2626';
                            errorTitle.style.margin = '0.5rem 0';
                            errorTitle.textContent = 'Error';

                            const errorMessage = document.createElement('p');
                            errorMessage.style.color = '#dc2626';
                            errorMessage.style.margin = '0';
                            errorMessage.textContent = data.error;

                            errorContainer.appendChild(errorIcon);
                            errorContainer.appendChild(errorTitle);
                            errorContainer.appendChild(errorMessage);

                            resultContent.appendChild(errorContainer);
                        }

                        resultSection.style.display = 'block';
                        resultSection.scrollIntoView({ behavior: 'smooth' });

                    } catch (error) {
                        // Network error
                        resultBox.className = 'result-box error';

                        // Clear previous content
                        resultContent.innerHTML = '';

                        const errorContainer = document.createElement('div');
                        errorContainer.style.textAlign = 'center';

                        const errorIcon = document.createElement('i');
                        errorIcon.className = 'fas fa-exclamation-circle text-red-500 text-2xl mb-2';

                        const errorTitle = document.createElement('h4');
                        errorTitle.style.color = '#dc2626';
                        errorTitle.style.margin = '0.5rem 0';
                        errorTitle.textContent = 'Network Error';

                        const errorMessage = document.createElement('p');
                        errorMessage.style.color = '#dc2626';
                        errorMessage.style.margin = '0';
                        errorMessage.textContent = 'Unable to connect to the server. Please try again.';

                        errorContainer.appendChild(errorIcon);
                        errorContainer.appendChild(errorTitle);
                        errorContainer.appendChild(errorMessage);

                        resultContent.appendChild(errorContainer);
                        resultSection.style.display = 'block';
                    } finally {
                        // Reset button
                        btn.disabled = false;
                        btn.innerHTML = '<i class="fas fa-expand-alt mr-2"></i>Expand Link';
                    }
                }

                function copyUrl(url) {
                    navigator.clipboard.writeText(url).then(() => {
                        // Find the copy button and show feedback
                        const buttons = document.querySelectorAll('.copy-btn');
                        buttons.forEach(btn => {
                            if (btn.onclick && btn.onclick.toString().includes(url)) {
                                const originalText = btn.innerHTML;
                                btn.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                                btn.classList.add('copied');

                                setTimeout(() => {
                                    btn.innerHTML = originalText;
                                    btn.classList.remove('copied');
                                }, 2000);
                            }
                        });
                    }).catch(() => {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = url;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                    });
                }

                // Allow Enter key to expand URL
                document.getElementById('shortUrl').addEventListener('keypress', function (e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        expandUrl();
                    }
                });
            </script>
</body>

</html>