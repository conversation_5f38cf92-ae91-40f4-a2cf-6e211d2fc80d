const { shortenUrl } = require('../middleware/functions');
const { redirect } = require('../middleware/redirect');
const Url = require('../models/Url');

// Configuration
const blacklist = ["https://chtlp.fr", "https://yu3.io"];
// Blacklisted domains
const shortenUrlController = async (req, res) => {
  const longUrl = req.body.longUrl;

  // Validate URL format
  try {
    new URL(longUrl);
  } catch (error) {
    return res.status(400).json({ error: 'Invalid URL format' });
  }

  // Check against blacklist
  if (blacklist.some(domain => longUrl.toLowerCase().includes(domain.toLowerCase()))) {
    return res.status(403).json({ error: 'Domain not allowed' });
  }

  try {
    const shortUrl = await shortenUrl(longUrl);
    res.json({ shortUrl });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

const redirectController = async (req, res) => {
  const shortId = req.params.shortId;
  try {
    const result = await redirect(shortId);
    if (result.url) {
      // Prevent indexing of short URL
      res.setHeader('X-Robots-Tag', 'noindex, nofollow');

      // Optional: Disable caching to prevent bots from storing the redirect
      res.setHeader('Cache-Control', 'no-store');
      res.redirect(result.url);
    } else {
      res.status(404).render('404');
    }
  } catch (error) {
    res.status(500).send('Internal server error');
  }
};

const expandUrlController = async (req, res) => {
  const { shortUrl } = req.body;

  if (!shortUrl) {
    return res.status(400).json({ error: 'Short URL is required' });
  }

  try {
    // Extract shortId from the URL
    let shortId;
    if (shortUrl.includes('/')) {
      // If it's a full URL like https://urldn.com/abc123
      shortId = shortUrl.split('/').pop();
    } else {
      // If it's just the shortId like abc123
      shortId = shortUrl;
    }

    // Find the URL in database
    const url = await Url.findOne({ shortId });

    if (!url) {
      return res.status(404).json({ error: 'Short URL not found' });
    }

    res.json({
      shortUrl: `${process.env.BASE_URL}/${shortId}`,
      longUrl: url.longUrl,
      clickCount: url.clickCount,
      shortId: shortId
    });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  shortenUrlController,
  redirectController,
  expandUrlController,
};
