<!DOCTYPE html>
<html lang="en">

<head>
    <%- include('../partials/seo', { title, description, url }) %>

        <script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": ["WebApplication", "SoftwareApplication"],
    "name": "Bulk URL Shortener Tool",
    "description": "Shorten multiple URLs at once with our free bulk URL shortener tool. Save time by processing multiple links simultaneously.",
    "url": "https://urldn.com/tools/bulk-shortener",
    "applicationCategory": "ProductivityApplication",
    "operatingSystem": "Web Browser",
    "requirements": "Requires JavaScript enabled in web browser",
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock",
        "priceValidUntil": "2026-12-31"
    },
    "featureList": [
        "Bulk URL shortening (up to 10 URLs)",
        "Real-time progress tracking",
        "Individual copy buttons",
        "Copy all URLs at once",
        "Error handling for invalid URLs",
        "Rate limiting protection"
    ],
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.7",
        "reviewCount": "18",
        "bestRating": "5",
        "worstRating": "1"
    },
    "publisher": {
        "@type": "Organization",
        "name": "URLdn",
        "url": "https://urldn.com",
        "logo": {
            "@type": "ImageObject",
            "url": "https://urldn.com/img/urldn-logo-1.webp",
            "width": "600",
            "height": "60"
        }
    },
    "breadcrumbList": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://urldn.com"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Tools",
                "item": "https://urldn.com/tools"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "Bulk Shortener",
                "item": "https://urldn.com/tools/bulk-shortener"
            }
        ]
    },
    "screenshot": {
        "@type": "ImageObject",
        "url": "https://urldn.com/img/bulk-shortener-screenshot.webp",
        "caption": "Bulk URL Shortener interface"
    }
}
</script>

        <style>
            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #374151;
            }

            .form-textarea {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #e5e7eb;
                border-radius: 0.5rem;
                font-size: 1rem;
                transition: border-color 0.2s;
                resize: vertical;
                min-height: 200px;
                font-family: 'Courier New', monospace;
            }

            .form-textarea:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .result-item {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 0.5rem;
                padding: 1rem;
                margin-bottom: 0.5rem;
            }

            .result-item.success {
                border-color: #10b981;
                background: #ecfdf5;
            }

            .result-item.error {
                border-color: #ef4444;
                background: #fef2f2;
            }

            .url-original {
                font-size: 0.875rem;
                color: #6b7280;
                word-break: break-all;
                margin-bottom: 0.5rem;
            }

            .url-short {
                font-weight: 600;
                color: #1f2937;
                word-break: break-all;
            }

            .url-short.error {
                color: #dc2626;
            }

            .copy-btn {
                background: #10b981;
                color: white;
                border: none;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                cursor: pointer;
                font-size: 0.75rem;
                margin-left: 0.5rem;
                transition: background-color 0.2s;
            }

            .copy-btn:hover {
                background: #059669;
            }

            .progress-bar {
                width: 100%;
                height: 8px;
                background: #e5e7eb;
                border-radius: 4px;
                overflow: hidden;
                margin: 1rem 0;
            }

            .progress-fill {
                height: 100%;
                background: #3b82f6;
                transition: width 0.3s ease;
                width: 0%;
            }

            .stats {
                display: flex;
                justify-content: space-around;
                background: #f3f4f6;
                padding: 1rem;
                border-radius: 0.5rem;
                margin: 1rem 0;
            }

            .stat-item {
                text-align: center;
            }

            .stat-number {
                font-size: 1.5rem;
                font-weight: bold;
                color: #1f2937;
            }

            .stat-label {
                font-size: 0.875rem;
                color: #6b7280;
            }
        </style>
</head>

<body>

    <%- include('../partials/header') %>

        <main class="container mx-auto px-4 py-8">
            <!-- Breadcrumb -->
            <nav class="mb-8 ">
                <ol class="flex items-center space-x-2 text-sm text-gray-600">
                    <li><a href="/" class="hover:text-blue-600">Home</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li><a href="/tools" class="hover:text-blue-600">Tools</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li class="text-gray-800 font-medium">Bulk Shortener</li>
                </ol>
            </nav>

            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                    Bulk URL <span class="text-green-600">Shortener</span>
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Shorten multiple URLs at once. Perfect for social media managers and marketers
                    who need to process many links quickly and efficiently.
                </p>
            </div>

            <!-- Bulk Shortener Form -->
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
                    <div class="form-group">
                        <label for="urlList" class="form-label">
                            Enter URLs (one per line)
                        </label>
                        <textarea id="urlList" class="form-textarea"
                            placeholder="https://example.com/page1&#10;https://example.com/page2&#10;https://example.com/page3&#10;..."></textarea>
                        <p class="text-sm text-gray-500 mt-1">
                            Enter each URL on a separate line. Maximum 10 URLs per batch.
                        </p>
                    </div>

                    <!-- Process Button -->
                    <div class="text-center">
                        <button onclick="processUrls()" id="processBtn"
                            class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 font-semibold">
                            <i class="fas fa-compress-alt mr-2"></i>Shorten URLs
                        </button>
                    </div>

                    <!-- Progress Section -->
                    <div id="progressSection" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-number" id="totalCount">0</div>
                                <div class="stat-label">Total</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="successCount">0</div>
                                <div class="stat-label">Success</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="errorCount">0</div>
                                <div class="stat-label">Errors</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="processedCount">0</div>
                                <div class="stat-label">Processed</div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div id="resultsSection" style="display: none;">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">Results:</h3>
                            <button onclick="copyAllResults()" class="copy-btn">
                                <i class="fas fa-copy mr-1"></i>Copy All Short URLs
                            </button>
                        </div>
                        <div id="results"></div>
                    </div>
                </div>

                <!-- Info Section -->
                <div class="mt-12 bg-green-50 rounded-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Bulk URL Shortening Tips</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="font-semibold mb-2">Best Practices:</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li>• One URL per line for best results</li>
                                <li>• Include http:// or https:// in each URL</li>
                                <li>• Maximum 10 URLs per batch</li>
                                <li>• Invalid URLs will be skipped with error message</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">Features:</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li>• Real-time progress tracking</li>
                                <li>• Individual copy buttons for each URL</li>
                                <li>• Copy all short URLs at once</li>
                                <li>• Error handling for invalid URLs</li>
                            </ul>
                        </div>

                    </div>
                    <div class="mt-16  text-center">
                        <h2 class="text-3xl font-bold text-gray-800 mb-6">Related Tools</h2>
                        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
                            <a href="/tools/utm-builder"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">UTM Builder</h3>
                                <p class="text-sm text-gray-600">Create tracking URLs</p>
                            </a>

                            <a href="/tools/link-expander"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-expand-alt text-purple-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">Link Expander</h3>
                                <p class="text-sm text-gray-600">Expand shortened URLs</p>
                            </a>

                            <a href="/tracking"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-orange-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-chart-bar text-orange-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">URL Tracking</h3>
                                <p class="text-sm text-gray-600">Track URL performance</p>
                            </a>

                            <a href="/qr-code-generator"
                                class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                                <div
                                    class="bg-indigo-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-qrcode text-indigo-600 text-xl"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-1">QR Code Generator</h3>
                                <p class="text-sm text-gray-600">Create QR codes</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </main>
        <span id="scrollTopBtn" onclick="scrollToTop()"><i class="fas fa-chevron-up"></i></span>

        <%- include('../partials/footer') %>

            <script>
                let processedUrls = [];

                async function processUrls() {
                    const urlText = document.getElementById('urlList').value.trim();
                    if (!urlText) {
                        alert('Please enter at least one URL');
                        return;
                    }

                    const urls = urlText.split('\n').map(url => url.trim()).filter(url => url);

                    if (urls.length === 0) {
                        alert('Please enter valid URLs');
                        return;
                    }

                    if (urls.length > 10) {
                        alert('Maximum 10 URLs allowed per batch');
                        return;
                    }

                    // Reset and show progress
                    processedUrls = [];
                    document.getElementById('progressSection').style.display = 'block';
                    document.getElementById('resultsSection').style.display = 'none';
                    document.getElementById('results').innerHTML = '';

                    // Update stats
                    document.getElementById('totalCount').textContent = urls.length;
                    document.getElementById('successCount').textContent = '0';
                    document.getElementById('errorCount').textContent = '0';
                    document.getElementById('processedCount').textContent = '0';
                    document.getElementById('progressFill').style.width = '0%';

                    // Disable button
                    const btn = document.getElementById('processBtn');
                    btn.disabled = true;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';

                    let successCount = 0;
                    let errorCount = 0;

                    // Process URLs one by one
                    for (let i = 0; i < urls.length; i++) {
                        const url = urls[i];
                        try {
                            const response = await fetch('/shorten', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({ longUrl: url })
                            });

                            const data = await response.json();

                            if (response.ok && data.shortUrl) {
                                processedUrls.push({
                                    original: url,
                                    short: data.shortUrl,
                                    success: true
                                });
                                successCount++;
                            } else {
                                processedUrls.push({
                                    original: url,
                                    error: data.error || 'Unknown error',
                                    success: false
                                });
                                errorCount++;
                            }
                        } catch (error) {
                            processedUrls.push({
                                original: url,
                                error: 'Network error',
                                success: false
                            });
                            errorCount++;
                        }

                        // Update progress
                        const processed = i + 1;
                        const progress = (processed / urls.length) * 100;
                        document.getElementById('progressFill').style.width = progress + '%';
                        document.getElementById('processedCount').textContent = processed;
                        document.getElementById('successCount').textContent = successCount;
                        document.getElementById('errorCount').textContent = errorCount;

                        // Small delay to prevent overwhelming the server
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    // Show results
                    displayResults();
                    document.getElementById('resultsSection').style.display = 'block';

                    // Re-enable button
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-compress-alt mr-2"></i>Shorten URLs';
                }

                function displayResults() {
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.innerHTML = '';

                    processedUrls.forEach((item, index) => {
                        const div = document.createElement('div');
                        div.className = `result-item ${item.success ? 'success' : 'error'}`;

                        if (item.success) {
                            div.innerHTML = `
                        <div class="url-original">${item.original}</div>
                        <div class="url-short">
                            ${item.short}
                            <button onclick="copyUrl('${item.short}')" class="copy-btn">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    `;
                        } else {
                            div.innerHTML = `
                        <div class="url-original">${item.original}</div>
                        <div class="url-short error">Error: ${item.error}</div>
                    `;
                        }

                        resultsDiv.appendChild(div);
                    });
                }

                function copyUrl(url) {
                    navigator.clipboard.writeText(url).then(() => {
                        // Visual feedback could be added here
                    }).catch(() => {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = url;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                    });
                }

                function copyAllResults() {
                    const shortUrls = processedUrls
                        .filter(item => item.success)
                        .map(item => item.short)
                        .join('\n');

                    if (shortUrls) {
                        navigator.clipboard.writeText(shortUrls).then(() => {
                            alert('All short URLs copied to clipboard!');
                        }).catch(() => {
                            // Fallback for older browsers
                            const textArea = document.createElement('textarea');
                            textArea.value = shortUrls;
                            document.body.appendChild(textArea);
                            textArea.select();
                            document.execCommand('copy');
                            document.body.removeChild(textArea);
                            alert('All short URLs copied to clipboard!');
                        });
                    } else {
                        alert('No successful URLs to copy');
                    }
                }
            </script>
</body>

</html>