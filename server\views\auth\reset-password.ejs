<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Google Tag Manager -->
  <script>(function (w, d, s, l, i) {
      w[l] = w[l] || []; w[l].push({
        'gtm.start':
          new Date().getTime(), event: 'gtm.js'
      }); var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-TXN2ZQQC');</script>
  <!-- End Google Tag Manager -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Reset Password</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <!-- Cloudflare Turnstile -->
  <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
  <!-- Add this CSS for better mobile input handling -->
  <style>
    @media (max-width: 640px) {
      input {
        font-size: 16px !important;
        /* Prevents iOS zoom on input focus */
      }

      .reset-password-form {
        margin: 1rem;
        max-width: calc(100% - 2rem);
      }
    }
  </style>
</head>

<body class="bg-gray-100 min-h-screen">
  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TXN2ZQQC" height="0" width="0"
      style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->


  <!-- Reset Password Form -->
  <div class="reset-password-form bg-white p-4 md:p-12 mx-auto my-4 md:my-8 rounded-lg shadow-lg w-full max-w-md">
    <div class="space-y-4 md:space-y-6">

      <!-- Logo -->
      <div class="flex justify-center mb-4 md:mb-6">
        <img src="/img/urldn-logo-1.webp" alt="Logo" class="w-24 h-12 md:w-30 md:h-15">
      </div>

      <!-- Friendly Welcome Message -->
      <h1 class="text-xl md:text-2xl font-bold text-center mb-3 md:mb-4">Reset Your Password</h1>

      <%- include('flash-messages.ejs') %>

        <!-- Reset Password Form -->
        <form action="/auth/reset-password/<%= token %>" method="POST" class="space-y-4 md:space-y-6" id="loading">
          <input type="hidden" name="_csrf" value="<%= csrfToken %>">

          <!-- New Password Field -->
          <div>
            <label class="block text-gray-700 text-sm font-semibold mb-2" for="password">New Password</label>
            <input
              class="w-full px-4 py-3 rounded-lg bg-gray-100 border-transparent focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500 text-gray-700 valider-password"
              id="password" type="password" autocomplete="new-password" name="password" required
              placeholder="Enter your new password">
          </div>

          <!-- Confirm Password Field -->
          <div>
            <label class="block text-gray-700 text-sm font-semibold mb-2" for="confirmPassword">Confirm
              Password</label>
            <input
              class="w-full px-4 py-3 rounded-lg bg-gray-100 border-transparent focus:border-blue-500 focus:bg-white focus:ring-2 focus:ring-blue-500 text-gray-700 valider-password"
              id="confirmPassword" type="password" autocomplete="new-password" name="confirmPassword" required
              placeholder="Confirm your new password">
          </div>

          <!-- Password Requirements -->
          <div class="px-2 md:px-0">
            <p class="text-sm text-gray-600 font-semibold mb-2">Your password should:</p>
            <ul class="text-xs md:text-sm text-gray-600 space-y-1">
              <li id="length" class="flex items-center">
                <svg class="w-3 h-3 md:w-4 md:h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm md:text-base">Be at least 8 characters long</span>
              </li>
              <li id="uppercase" class="flex items-center text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Include an uppercase letter
              </li>
              <li id="lowercase" class="flex items-center text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Include a lowercase letter
              </li>
              <li id="number" class="flex items-center text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Include a number
              </li>
              <li id="specialChar" class="flex items-center text-base">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Include a special character
              </li>
            </ul>
          </div>
          <div class="cf-turnstile" data-sitekey="0x4AAAAAAA_DFk3ei4SmzDjq"></div>

          <!-- Reset Password Button -->
          <button
            class="w-full py-2.5 md:py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold rounded-lg hover:from-blue-600 hover:to-purple-700 transition duration-300 text-sm md:text-base"
            type="submit" id="submitBtn">

            <span>Reset Password</span>
          </button>

        </form>

        <!-- Back to Login Link -->
        <div class="mt-4">
          <p class="text-center text-gray-600 text-xs md:text-sm">
            Remember your password?
            <a href="/auth/login" class="text-blue-600 hover:text-blue-800 font-semibold">Login</a>
          </p>
        </div>
    </div>
  </div>

  <%- include('loading.ejs') %>
</body>

</html>