<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-TXN2ZQQC');</script>
    <!-- End Google Tag Manager -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        <%= currentPage===1 ? 'Blog - Tips & Insights on URL Shortening' : `Blog - Tips & Insights on URL Shortening -
            Page ${currentPage}` %>
    </title>
    <link rel="icon" href="/favicon.ico" sizes="64x64 32x32 24x24 16x16" type="image/x-icon">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="canonical" href="<%= canonicalUrl %>" />
    <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="index,follow" />
    <meta name="description"
        content="<%= currentPage === 1 ? 'Explore our blog for tips, insights, and updates about URL shortening and how it benefits. Stay informed with the latest trends and best practices.' : `Explore our blog for tips, insights, and updates about URL shortening. Page ${currentPage} of our blog.` %>">
    <meta name="keywords" content="blog, URL shortener tips, e-commerce insights, link management, URL shortening news">
    <meta property="og:title"
        content="<%= currentPage === 1 ? 'Blog - Tips & Insights on URL Shortening' : `Blog - Tips & Insights on URL Shortening - Page ${currentPage}` %>">
    <meta property="og:description"
        content="Read our blog to learn about URL shortening, link management strategies, and how they can enhance your e-commerce business.">
    <meta property="og:image" content="/img/urldn-logo-1.webp">
    <meta property="og:url" content="<%= canonicalUrl %>">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title"
        content="<%= currentPage === 1 ? 'Blog - Tips & Insights on URL Shortening' : `Blog - Tips & Insights on URL Shortening - Page ${currentPage}` %>">
    <meta name="twitter:description"
        content="Get the latest tips and insights on URL shortening and link management through our informative blog.">
    <meta name="twitter:image" content="/img/urldn-logo-1.webp">
    <link rel="preload" href="/styles/style.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">
    <link rel="preload" href="<%= featuredPosts[0].image %>" as="image">
    <link rel="stylesheet" href="/styles/style.css">
    <link rel="stylesheet" href="/styles/blog.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js" defer></script>
    <script src="/js/same.js" defer></script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "WebPage",
          "@id": "<%= canonicalUrl %>",
          "url": "<%= canonicalUrl %>",
          "name": "<%= currentPage === 1 ? 'Blog - Tips & Insights on URL Shortening' : 'Blog - Tips & Insights on URL Shortening - Page ' + currentPage %>",
          "isPartOf": {"@id": "https://urldn.com/blog/#website"},
          "about": {"@id": "https://urldn.com/blog/#organization"},
          "datePublished": "2024-01-01T00:00:00+00:00",
          "dateModified": "2024-01-21T00:00:00+00:00",
          "description": "<%= currentPage === 1 ? 'Explore our blog for tips, insights, and updates about URL shortening and how it benefits. Stay informed with the latest trends and best practices.' : 'Explore our blog for tips, insights, and updates about URL shortening. Page ' + currentPage + ' of our blog.' %>",
          "breadcrumb": {"@id": "<%= canonicalUrl %>#breadcrumb"},
          "inLanguage": "en-US",
          "potentialAction": [{"@type": "ReadAction", "target": ["<%= canonicalUrl %>"]}]
        },
        {
          "@type": "BreadcrumbList",
          "@id": "<%= canonicalUrl %>#breadcrumb",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Home",
              "item": "https://urldn.com/"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "Blog",
              "item": "https://urldn.com/blog/"
            }
            <%_ if (currentPage > 1) { _%>
            ,{
              "@type": "ListItem",
              "position": 3,
              "name": "Page <%= currentPage %>",
              "item": "<%= canonicalUrl %>"
            }
            <%_ } _%>
          ]
        },
        {
          "@type": "WebSite",
          "@id": "https://urldn.com/blog/#website",
          "url": "https://urldn.com/blog/",
          "name": "URLDN | Blog",
          "description": "Welcome to the official URLDN blog, covering tips, insights, and updates about URL shortening and link management.",
          "publisher": {"@id": "https://urldn.com/blog/#organization"},
          "inLanguage": "en-US"
        },
        {
          "@type": "Organization",
          "@id": "https://urldn.com/blog/#organization",
          "name": "URLDN",
          "url": "https://urldn.com/",
          "logo": {
            "@type": "ImageObject",
            "inLanguage": "en-US",
            "@id": "https://urldn.com/blog/#/schema/logo/image/",
            "url": "https://urldn.com/img/urldn-logo-1.webp",
            "contentUrl": "https://urldn.com/img/urldn-logo-1.webp",
            "width": 280,
            "height": 280,
            "caption": "URLDN"
          },
          "image": {"@id": "https://urldn.com/blog/#/schema/logo/image/"},
          "sameAs": [
            "https://x.com/urldn_",
            "https://www.instagram.com/urldn/",
            "https://www.linkedin.com/company/urldn/",
            "https://www.youtube.com/channel/urldn"
          ]
        }
      ]
    }
    </script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8521338629167059"
        crossorigin="anonymous"></script>
</head>

<body>
    <%- include('./partials/header') %>
        <main class="container">
            <section id="featured-post">
                <h1 class="related-articles-heading">Related Articles</h1>
                <div id="featured-post-content" class="slick-carousel" aria-label="Featured Posts Carousel">
                    <% featuredPosts.forEach(post=> { %>
                        <a href="/blog/<%= post.slug %>" class="featured-post">
                            <img src="<%= post.image %>" srcset="<%= post.image %> 1200w"
                                sizes="(max-width: 600px) 400px, (max-width: 1200px) 800px, 1200px"
                                alt="<%= post.title %>" class="featured-image" width="1200" height="630" loading="lazy"
                                decoding="async" />
                            <h3 class="featured-title">
                                <%= post.title %>
                            </h3>
                        </a>
                        <% }) %>
                </div>
            </section>
            <h2 id="blogheading"><span class="highlight">Articles</span></h2>
            <div id="categoryButtons">
                <span class="category-btn"><i class="fas fa-folder-open"></i>Categories</span>
                <a href="/blog/category/analytics" class="category-btn" rel="noopener noreferrer"><i
                        class="fas fa-chart-bar"></i> Analytics</a>
                <a href="/blog/category/influencers" class="category-btn" rel="noopener noreferrer"><i
                        class="fas fa-user-circle"></i>Influencers</a>
                <a href="/blog/category/marketing" class="category-btn" rel="noopener noreferrer"><i
                        class="fas fa-bullhorn"></i> Marketing</a>
                <a href="/blog/category/urldn" class="category-btn" rel="noopener noreferrer"><i
                        class="fas fa-link"></i> urldn</a>
                <a href="/blog/category/qrcodes" class="category-btn" rel="noopener noreferrer"><i
                        class="fas fas fa-qrcode"></i> Qr Codes</a>
                <a href="/blog/category/tools" class="category-btn" rel="noopener noreferrer"><i
                        class="fas fa-tools"></i> Tools</a>
                <a href="/blog/category/howto" class="category-btn" rel="noopener noreferrer"><i
                        class="fas fas fa-chalkboard-teacher"></i> How
                    To</a>
            </div>
            <div id="more-articles-container">
                <% blogs.forEach(blog=> { %>
                    <article class="article-home edit-page">
                        <a href="/blog/<%= blog.slug %>">
                            <img src="<%= blog.image %>" alt="<%= blog.title %>" width="1200" height="630"
                                loading="lazy" decoding="async" class="blog-thumbnail" />
                            <h2>
                                <%= blog.title %>
                            </h2>
                            <p class="blog-content" role="article">
                                <%- blog.content %>
                            </p>

                        </a>
                    </article>
                    <% }) %>
            </div>
            <!-- Pagination -->
            <div id="pagination">
                <!-- Previous Button -->
                <% if (currentPage> 1) { %>
                    <a href="<%= currentPage - 1 === 1 ? '/blog' : `/blog?page=${currentPage - 1}` %>" id="prev-page"
                        class="pagination-link">
                        Previous
                    </a>
                    <% } else { %>
                        <span class="pagination-disabled">Previous</span>
                        <% } %>

                            <!-- First Page -->
                            <% if (currentPage> 3) { %>
                                <a href="/blog" class="pagination-link">1</a>
                                <% if (currentPage> 4) { %>
                                    <span class="ellipsis">...</span>
                                    <% } %>
                                        <% } %>

                                            <!-- Page Links -->
                                            <% for (let i=Math.max(1, currentPage - 2); i <=Math.min(totalPages,
                                                currentPage + 2); i++) { %>
                                                <% if (i===currentPage) { %>
                                                    <span class="pagination-current">
                                                        <%= i %>
                                                    </span>
                                                    <% } else { %>
                                                        <a href="<%= i === 1 ? '/blog' : `/blog?page=${i}` %>"
                                                            class="pagination-link">
                                                            <%= i %>
                                                        </a>
                                                        <% } %>
                                                            <% } %>

                                                                <!-- Last Page -->
                                                                <% if (currentPage < totalPages - 2) { %>
                                                                    <% if (currentPage < totalPages - 3) { %>
                                                                        <span class="ellipsis">...</span>
                                                                        <% } %>
                                                                            <a href="/blog?page=<%= totalPages %>"
                                                                                class="pagination-link">
                                                                                <%= totalPages %>
                                                                            </a>
                                                                            <% } %>

                                                                                <!-- Next Button -->
                                                                                <% if (currentPage < totalPages) { %>
                                                                                    <a href="/blog?page=<%= currentPage + 1 %>"
                                                                                        id="next-page"
                                                                                        class="pagination-link">
                                                                                        Next
                                                                                    </a>
                                                                                    <% } else { %>
                                                                                        <span
                                                                                            class="pagination-disabled">Next</span>
                                                                                        <% } %>
            </div>
        </main>
        <%- include('./partials/footer') %>
            <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ItemList",
      "itemListElement": [
        <% featuredPosts.forEach((post, index) => { %>
        {
          "@type": "ListItem",
          "position": <%= index + 1 %>,
          "item": {
            "@type": "BlogPosting",
            "@id": "https://urldn.com/blog/<%= post.slug %>",
            "url": "https://urldn.com/blog/<%= post.slug %>",
            "headline": "<%= post.title %>",
            "image": {
              "@type": "ImageObject",
              "url": "<%= post.image %>",
              "width": 1200,
              "height": 630
            },
            "description": "<%= post.description || post.content.replace(/<[^>]*>/g, '').trim().substring(0, 200) + '...' %>",
            "author": {
              "@type": "Person",
              "name": "<%= post.author %>",
              "url": "https://urldn.com/blog/author/urldn"
            },
            "publisher": {
              "@type": "Organization",
              "name": "URLdn",
              "logo": {
                "@type": "ImageObject",
                "url": "https://urldn.com/img/urldn-logo-1.webp",
                "width": 280,
                "height": 280
              }
            },
            "datePublished": "<%= new Date(post.createdAt).toISOString() %>",
            "dateModified": "<%= new Date(post.updatedAt).toISOString() %>",
            "articleSection": "<%= post.category %>",
            "inLanguage": "en-US"
          }
        }<%= index < featuredPosts.length - 1 ? ',' : '' %>
        <% }) %>
      ]
    }
    </script>
            <span id="scrollTopBtn" onclick="scrollToTop()"><i class="fas fa-chevron-up"></i></span>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>

            <script>
                $(document).ready(function () {
                    $('.slick-carousel').slick({
                        dots: true,
                        infinite: true,
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        prevArrow: false,
                        nextArrow: false,
                        autoplay: true,
                        autoplaySpeed: 3000,

                    });
                });
            </script>


</body>

</html>