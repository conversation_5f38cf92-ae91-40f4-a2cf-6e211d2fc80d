const mongoose = require('mongoose');

const blogSchema = new mongoose.Schema({
    title: { type: String, required: true }, // For <title> tag and meta
    h1: { type: String, required: true }, // For <h1> tag (different from title)
    content: { type: String, required: true },
    image: { type: String },
    slug: { type: String, required: true, unique: true }, // Custom slug input
    author: { type: String, default: 'urldn' },
    category: { type: String, required: true },
    metaDescription: { type: String }, // Optional custom meta description
    featured: { type: Boolean, default: false } // For featured posts
}, { timestamps: true });

module.exports = mongoose.model('Blog', blogSchema);
