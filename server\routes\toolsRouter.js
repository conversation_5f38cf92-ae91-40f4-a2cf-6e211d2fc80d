const express = require('express');
const router = express.Router();
const rateLimit = require('express-rate-limit');

// Rate limiter for tools pages
const toolsPageLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Rate limiter for tool actions (more restrictive)
const toolActionLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 20, // limit each IP to 20 actions per minute
  message: 'Too many tool actions from this IP, please try again later.'
});

// Apply rate limiting to all tool routes
router.use(toolsPageLimiter);

// Tools index page - lists all available tools
router.get('/', (req, res) => {
  res.render('tools/index', {
    title: 'Free URL Tools ',
    description: 'Discover powerful free URL tools including UTM builder, bulk URL shortener, and link expander. Streamline your link management with urldn.com.',
    url: 'https://urldn.com/tools/'
  });
});

// UTM Builder tool
router.get('/utm-builder', (req, res) => {
  res.render('tools/utm-builder', {
    title: 'UTM Builder ',
    description: 'Create UTM tracking URLs easily with our free UTM builder tool. Track your marketing campaigns with custom UTM parameters for better analytics.',
    url: 'https://urldn.com/tools/utm-builder'
  });
});

// Bulk URL Shortener tool
router.get('/bulk-shortener', (req, res) => {
  res.render('tools/bulk-shortener', {
    title: 'Bulk URL Shortener – Shorten Multiple Links',
    description: 'Shorten multiple URLs at once with our free bulk URL shortener tool. Save time by processing multiple links simultaneously.',
    url: 'https://urldn.com/tools/bulk-shortener'
  });
});

// Link Expander tool
router.get('/link-expander', (req, res) => {
  res.render('tools/link-expander', {
    title: 'Link Expander ',
    description: 'Expand shortened URLs to see the original destination before clicking. Check where short links lead with our free link expander tool.',
    url: 'https://urldn.com/tools/link-expander'
  });
});

module.exports = router;
