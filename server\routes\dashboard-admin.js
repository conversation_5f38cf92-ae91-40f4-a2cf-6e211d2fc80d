const express = require('express');
const { authenticateToken } = require('../middleware/authMiddleware');
const User = require('../models/User');
const Link = require('../models/Link');
const Blog = require('../models/blogModel');
const Url = require('../models/Url');
const {
    generateSEOReport,
    checkDuplicateTitles,
    validateStructuredData,
    checkSitemapHealth,
    validateHreflang,
    checkPerformanceMetrics
} = require('../utils/seoMonitor');
const router = express.Router();
// Apply authentication to all dashboard routes
router.use(authenticateToken);
// Dashboard Overview
router.get('/', async (req, res) => {
    try {
        // Fetch total counts
        const totalUsers = await User.countDocuments();
        const totalAnyUrls = await Url.countDocuments();
        const totalLinks = await Link.countDocuments();
        const totalPosts = await Blog.countDocuments();

        // Fetch recent users, links, and posts
        const recentUsers = await User.find()
            .sort({ createdAt: -1 })
            .limit(5)
            .select('username email createdAt');

        const recentLinks = await Link.find()
            .sort({ createdAt: -1 })
            .limit(5)
            .populate('createdBy', 'username');
        const recentUrls = await Url.find()
            .sort({ createdAt: -1 })
            .limit(5)


        const recentPosts = await Blog.find()
            .sort({ createdAt: -1 })
            .limit(5)
            .select('title category createdAt slug');

        // Prepare stats object
        const stats = {
            totalUsers,
            totalLinks,
            totalPosts,
            recentUsers,
            recentLinks,
            recentPosts,
            totalAnyUrls,
            recentUrls
        };

        // Render the dashboard.ejs file with the stats and username
        res.render('dash-admin/dashboard', {
            stats,
            username: req.user.username, // Assuming the username is stored in req.user
            messages: req.flash() // Pass flash messages to the view
        });
    } catch (error) {
        req.flash('error', 'An error occurred while loading the dashboard.');
        res.status(500).json({ success: false, message: error.message });
    }
});
// Global Search Route
router.get('/search', async (req, res) => {
    try {
        const searchQuery = req.query.q || '';
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        if (!searchQuery) {
            return res.render('dash-admin/search', {
                results: [],
                searchQuery: '',
                currentPage: page,
                totalPages: 0,
                totalResults: 0,
                messages: req.flash()
            });
        }

        const searchRegex = new RegExp(searchQuery, 'i');

        const [users, links, blogs, urls] = await Promise.all([
            User.find({
                $or: [
                    { email: searchRegex },
                    { username: searchRegex }
                ]
            }).limit(limit).skip(skip),

            Link.find({
                $or: [
                    { shortCode: searchRegex },
                    { originalUrl: searchRegex }
                ]
            }).limit(limit).skip(skip),

            Blog.find({
                $or: [
                    { title: searchRegex },
                    { content: searchRegex }
                ]
            }).limit(limit).skip(skip),

            Url.find({
                $or: [
                    { shortId: searchRegex },
                    { longUrl: searchRegex }
                ]
            }).limit(limit).skip(skip)
        ]);

        const results = [
            ...users.map(item => ({
                type: 'user',
                data: item,
                id: item._id
            })),
            ...links.map(item => ({
                type: 'link',
                data: item,
                id: item._id
            })),
            ...blogs.map(item => ({
                type: 'blog',
                data: item,
                id: item._id
            })),
            ...urls.map(item => ({
                type: 'url',
                data: item,
                id: item.shortId
            }))
        ];

        const totalResults = results.length;
        const totalPages = Math.ceil(totalResults / limit);

        res.render('dash-admin/search', {
            results,
            searchQuery,
            currentPage: page,
            totalPages,
            totalResults,
            messages: req.flash()
        });

    } catch (error) {
        console.error('Search error:', error);
        req.flash('error', 'An error occurred while searching');
        res.render('dash-admin/search', {
            results: [],
            searchQuery: req.query.q || '',
            currentPage: 1,
            totalPages: 0,
            totalResults: 0,
            messages: req.flash()
        });
    }
});
// Users Management Route
router.get('/users', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        // Ensure req.user exists after authentication middleware
        if (!req.user) {
            return res.redirect('/login'); // Redirect if user is not authenticated
        }

        // Fetch users with pagination, excluding passwords
        const users = await User.find().skip(skip).limit(limit).select('-password');
        const totalUsers = await User.countDocuments();
        const totalPages = Math.ceil(totalUsers / limit);

        // Render the users list
        res.render('dash-admin/users', {
            users,
            currentPage: page,
            totalPages: totalPages,
            username: req.user.email, // Pass the logged-in admin email
            messages: req.flash() // Pass flash messages to the view
        });
    } catch (err) {
        req.flash('error', 'An error occurred while fetching users.');
        console.error(err);
        res.status(500).send('Server error');
    }
});

// Delete user route
router.post('/users/:id/delete', async (req, res) => {
    try {
        const user = await User.findByIdAndDelete(req.params.id);
        if (!user) {
            req.flash('error', 'User not found.');
            return res.redirect('/dash-admin/dashboard/users');
        }
        req.flash('success', 'User deleted successfully.');
        res.redirect('/dash-admin/dashboard/users');
    } catch (error) {
        req.flash('error', 'Error deleting user.');
        res.status(500).json({ success: false, message: 'Error deleting user' });
    }
});

// SEO Dashboard Route
router.get('/seo', async (req, res) => {
    try {
        const [
            seoReport,
            sitemapHealth,
            hreflangValidation,
            performanceMetrics
        ] = await Promise.all([
            generateSEOReport(),
            checkSitemapHealth(),
            validateHreflang(),
            checkPerformanceMetrics()
        ]);

        res.render('dash-admin/seo-dashboard', {
            seoReport,
            sitemapHealth,
            hreflangValidation,
            performanceMetrics,
            username: req.user.username,
            messages: req.flash()
        });
    } catch (error) {
        console.error('Error loading SEO dashboard:', error);
        req.flash('error', 'Failed to load SEO dashboard');
        res.redirect('/dash-admin/dashboard');
    }
});

// SEO API endpoints for AJAX calls
router.get('/seo/api/report', async (req, res) => {
    try {
        const report = await generateSEOReport();
        res.json({ status: 'success', data: report });
    } catch (error) {
        res.status(500).json({ status: 'error', message: error.message });
    }
});

router.get('/seo/api/duplicates', async (req, res) => {
    try {
        const result = await checkDuplicateTitles();
        res.json({ status: 'success', data: result });
    } catch (error) {
        res.status(500).json({ status: 'error', message: error.message });
    }
});

router.get('/seo/api/validate/:slug', async (req, res) => {
    try {
        const result = await validateStructuredData(req.params.slug);
        res.json({ status: 'success', data: result });
    } catch (error) {
        res.status(500).json({ status: 'error', message: error.message });
    }
});

router.get('/seo/api/sitemap-health', async (req, res) => {
    try {
        const result = await checkSitemapHealth();
        res.json({ status: 'success', data: result });
    } catch (error) {
        res.status(500).json({ status: 'error', message: error.message });
    }
});

// Fix missing SEO fields in existing blog posts
router.post('/seo/api/fix-missing-fields', async (req, res) => {
    try {
        const blogs = await Blog.find({});
        let fixedCount = 0;
        const results = [];

        for (const blog of blogs) {
            let updated = false;
            const changes = [];

            // Add H1 if missing (use title as fallback)
            if (!blog.h1) {
                blog.h1 = blog.title;
                updated = true;
                changes.push('Added H1 heading');
            }

            // Add metaDescription if missing (generate from content)
            if (!blog.metaDescription) {
                const cleanContent = blog.content.replace(/<[^>]*>/g, '').trim();
                blog.metaDescription = cleanContent.substring(0, 160) + (cleanContent.length > 160 ? '...' : '');
                updated = true;
                changes.push('Added meta description');
            }

            // Add featured field if missing
            if (blog.featured === undefined) {
                blog.featured = false;
                updated = true;
                changes.push('Added featured flag');
            }

            if (updated) {
                await blog.save();
                fixedCount++;
                results.push({
                    slug: blog.slug,
                    title: blog.title,
                    changes: changes
                });
            }
        }

        res.json({
            status: 'success',
            data: {
                totalProcessed: blogs.length,
                fixedCount,
                results
            }
        });
    } catch (error) {
        console.error('Error fixing missing fields:', error);
        res.status(500).json({ status: 'error', message: error.message });
    }
});

// Blog Management Routes
router.get('/blogs', async (req, res) => {
    try {
        res.render('dash-admin/blogs');
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
});

// Links Management Routes
router.get('/links', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        // Ensure req.user exists after authentication middleware
        if (!req.user) {
            return res.redirect('/login'); // Redirect if user is not authenticated
        }

        const links = await Link.find()
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);

        const total = await Link.countDocuments();
        const totalPages = Math.ceil(total / limit);

        // Render the links management page
        res.render('dash-admin/links', {
            links,
            currentPage: page,
            totalPages: totalPages,
            username: req.user.email, // Pass the logged-in admin email
            messages: req.flash() // Pass flash messages to the view
        });
    } catch (error) {
        req.flash('error', 'An error occurred while fetching links.');
        res.status(500).send('Server error');
    }
});

// Delete link route
router.delete('/links/:id', async (req, res) => {
    try {
        const link = await Link.findByIdAndDelete(req.params.id);
        if (!link) {
            req.flash('error', 'Link not found.');
            return res.json({ success: false, message: 'Link not found' });
        }
        req.flash('success', 'Link deleted successfully.');
        res.json({ success: true, message: 'Link deleted successfully' });
    } catch (error) {
        req.flash('error', 'Error deleting link.');
        res.status(500).json({ success: false, message: error.message });
    }
});

// Anonymous URLs Management
router.get('/anonymous-urls', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        const urls = await Url.find()
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit);

        const totalUrls = await Url.countDocuments();
        const totalPages = Math.ceil(totalUrls / limit);

        res.render('dash-admin/anonymous-urls', {
            urls,
            currentPage: page,
            totalPages,
            totalUrls,
            messages: req.flash() // Pass flash messages to the view
        });
    } catch (error) {
        req.flash('error', 'An error occurred while fetching anonymous URLs.');
        console.error(error);
        res.status(500).send('Server error');
    }
});

// Delete anonymous URL
router.post('/anonymous-urls/:shortId/delete', async (req, res) => {
    try {
        const url = await Url.findOneAndDelete({ shortId: req.params.shortId });
        if (!url) {
            req.flash('error', 'URL not found.');
            return res.redirect('/dash-admin/dashboard/anonymous-urls');
        }
        req.flash('success', 'URL deleted successfully.');
        res.redirect('/dash-admin/dashboard/anonymous-urls');
    } catch (error) {
        req.flash('error', 'Error deleting URL.');
        console.error('Error deleting URL:', error);
        res.redirect('/dash-admin/dashboard/anonymous-urls');
    }
});

// Add new delete routes for search results
router.delete('/search/:type/:id', async (req, res) => {
    const { type, id } = req.params;

    try {
        let result;
        switch (type) {
            case 'user':
                result = await User.findByIdAndDelete(id);
                break;
            case 'link':
                result = await Link.findByIdAndDelete(id);
                break;
            case 'blog':
                result = await Blog.findByIdAndDelete(id);
                break;
            case 'url':
                result = await Url.findOneAndDelete({ shortId: id });
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid type'
                });
        }

        if (!result) {
            return res.status(404).json({
                success: false,
                message: `${type} not found`
            });
        }

        res.json({
            success: true,
            message: `${type} deleted successfully`,
            deletedId: id,
            type: type
        });

    } catch (error) {
        console.error(`Error deleting ${type}:`, error);
        res.status(500).json({
            success: false,
            message: `Error deleting ${type}`
        });
    }
});

module.exports = router;