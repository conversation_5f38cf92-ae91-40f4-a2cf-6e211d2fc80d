<!DOCTYPE html>
<html lang="en">

<head>
    <%- include('../partials/seo', { title, description, url }) %>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": ["WebApplication", "SoftwareApplication"],
    "name": "UTM Builder Tool",
    "description": "Create UTM tracking URLs easily with our free UTM builder tool. Track your marketing campaigns with custom UTM parameters for better analytics.",
    "url": "https://urldn.com/tools/utm-builder",
    "applicationCategory": "MarketingApplication",
    "operatingSystem": "Web Browser",
    "requirements": "Requires JavaScript enabled in web browser",
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
    },
    "featureList": [
        "UTM Source tracking",
        "UTM Medium tracking",
        "UTM Campaign tracking",
        "UTM Term tracking",
        "UTM Content tracking",
        "Real-time URL generation",
        "Copy to clipboard functionality"
    ],
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "25"
    },
    "publisher": {
        "@type": "Organization",
        "name": "URLdn",
        "url": "https://urldn.com",
        "logo": {
            "@type": "ImageObject",
            "url": "https://urldn.com/img/urldn-logo-1.webp"
        }
    },
    "breadcrumbList": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://urldn.com"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Tools",
                "item": "https://urldn.com/tools"
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": "UTM Builder",
                "item": "https://urldn.com/tools/utm-builder"
            }
        ]
    }
}
</script>

        <style>
            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #374151;
            }

            .form-input {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #e5e7eb;
                border-radius: 0.5rem;
                font-size: 1rem;
                transition: border-color 0.2s;
            }

            .form-input:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            .result-box {
                background: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 0.5rem;
                padding: 1rem;
                margin-top: 1rem;
                word-break: break-all;
            }

            .copy-btn {
                background: #10b981;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 0.375rem;
                cursor: pointer;
                margin-top: 0.5rem;
                transition: background-color 0.2s;
            }

            .copy-btn:hover {
                background: #059669;
            }

            .copy-btn.copied {
                background: #6b7280;
            }
        </style>
</head>

<body>

    <%- include('../partials/header') %>

        <main class="container mx-auto px-4 py-8">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm text-gray-600">
                    <li><a href="/" class="hover:text-blue-600">Home</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li><a href="/tools" class="hover:text-blue-600">Tools</a></li>
                    <li><i class="fas fa-chevron-right text-xs"></i></li>
                    <li class="text-gray-800 font-medium">UTM Builder</li>
                </ol>
            </nav>

            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                    UTM <span class="text-blue-600">Builder</span>
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Create UTM tracking URLs to monitor your marketing campaigns.
                    Track traffic sources, mediums, and campaign performance with ease.
                </p>
            </div>

            <!-- UTM Builder Form -->
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg p-8 border border-gray-200">
                    <form id="utmForm">
                        <div class="grid md:grid-cols-2 gap-6">
                            <!-- Website URL -->
                            <div class="form-group md:col-span-2">
                                <label for="url" class="form-label">
                                    Website URL <span class="text-red-500">*</span>
                                </label>
                                <input type="url" id="url" class="form-input" placeholder="https://example.com"
                                    required>
                                <p class="text-sm text-gray-500 mt-1">The full website URL you want to track</p>
                            </div>

                            <!-- Campaign Source -->
                            <div class="form-group">
                                <label for="source" class="form-label">
                                    Campaign Source <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="source" class="form-input"
                                    placeholder="google, facebook, newsletter" required>
                                <p class="text-sm text-gray-500 mt-1">Where the traffic comes from (e.g., google,
                                    facebook)</p>
                            </div>

                            <!-- Campaign Medium -->
                            <div class="form-group">
                                <label for="medium" class="form-label">
                                    Campaign Medium <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="medium" class="form-input" placeholder="cpc, email, social"
                                    required>
                                <p class="text-sm text-gray-500 mt-1">Marketing medium (e.g., cpc, email, social)</p>
                            </div>

                            <!-- Campaign Name -->
                            <div class="form-group">
                                <label for="campaign" class="form-label">
                                    Campaign Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="campaign" class="form-input"
                                    placeholder="summer_sale, product_launch" required>
                                <p class="text-sm text-gray-500 mt-1">Specific campaign name for tracking</p>
                            </div>

                            <!-- Campaign Term -->
                            <div class="form-group">
                                <label for="term" class="form-label">Campaign Term</label>
                                <input type="text" id="term" class="form-input"
                                    placeholder="running+shoes, blue+widgets">
                                <p class="text-sm text-gray-500 mt-1">Paid search keywords (optional)</p>
                            </div>

                            <!-- Campaign Content -->
                            <div class="form-group md:col-span-2">
                                <label for="content" class="form-label">Campaign Content</label>
                                <input type="text" id="content" class="form-input" placeholder="logolink, textlink">
                                <p class="text-sm text-gray-500 mt-1">Differentiate similar content or links (optional)
                                </p>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <div class="text-center mt-8">
                            <button type="button" onclick="generateUTM()"
                                class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-semibold">
                                <i class="fas fa-link mr-2"></i>Generate UTM URL
                            </button>
                        </div>
                    </form>

                    <!-- Result Section -->
                    <div id="resultSection" class="mt-8" style="display: none;">
                        <h3 class="text-lg font-semibold mb-4">Your UTM URL:</h3>
                        <div class="result-box">
                            <div id="utmResult" class="font-mono text-sm"></div>
                            <button onclick="copyToClipboard()" class="copy-btn" id="copyBtn">
                                <i class="fas fa-copy mr-1"></i>Copy URL
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Info Section -->
                <div class="mt-12 bg-blue-50 rounded-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">What are UTM Parameters?</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="font-semibold mb-2">Required Parameters:</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li><strong>Source:</strong> Identifies the advertiser, site, publication, etc.</li>
                                <li><strong>Medium:</strong> The advertising or marketing medium</li>
                                <li><strong>Campaign:</strong> The individual campaign name</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">Optional Parameters:</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li><strong>Term:</strong> The paid keywords</li>
                                <li><strong>Content:</strong> Used to differentiate similar content</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Related Tools Section -->
                <div class="mt-16 text-center">
                    <h2 class="text-3xl font-bold text-gray-800 mb-6">Related Tools</h2>
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
                        <a href="/tools/bulk-shortener"
                            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                            <div
                                class="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-compress-alt text-green-600 text-xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">Bulk Shortener</h3>
                            <p class="text-sm text-gray-600">Shorten multiple URLs</p>
                        </a>

                        <a href="/tools/link-expander"
                            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                            <div
                                class="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-expand-alt text-purple-600 text-xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">Link Expander</h3>
                            <p class="text-sm text-gray-600">Expand shortened URLs</p>
                        </a>

                        <a href="/tracking"
                            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                            <div
                                class="bg-orange-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-chart-bar text-orange-600 text-xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">URL Tracking</h3>
                            <p class="text-sm text-gray-600">Track URL performance</p>
                        </a>

                        <a href="/qr-code-generator"
                            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-4 border border-gray-200 text-center">
                            <div
                                class="bg-indigo-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-qrcode text-indigo-600 text-xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">QR Code Generator</h3>
                            <p class="text-sm text-gray-600">Create QR codes</p>
                        </a>
                    </div>
                </div>
            </div>
        </main>
        <span id="scrollTopBtn" onclick="scrollToTop()"><i class="fas fa-chevron-up"></i></span>

        <%- include('../partials/footer') %>

            <script>
                function generateUTM() {
                    const url = document.getElementById('url').value.trim();
                    const source = document.getElementById('source').value.trim();
                    const medium = document.getElementById('medium').value.trim();
                    const campaign = document.getElementById('campaign').value.trim();
                    const term = document.getElementById('term').value.trim();
                    const content = document.getElementById('content').value.trim();

                    // Validate required fields
                    if (!url || !source || !medium || !campaign) {
                        alert('Please fill in all required fields (URL, Source, Medium, Campaign)');
                        return;
                    }

                    // Validate URL format
                    try {
                        new URL(url);
                    } catch (e) {
                        alert('Please enter a valid URL');
                        return;
                    }

                    // Build UTM URL
                    const urlObj = new URL(url);
                    urlObj.searchParams.set('utm_source', source);
                    urlObj.searchParams.set('utm_medium', medium);
                    urlObj.searchParams.set('utm_campaign', campaign);

                    if (term) urlObj.searchParams.set('utm_term', term);
                    if (content) urlObj.searchParams.set('utm_content', content);

                    const utmUrl = urlObj.toString();

                    // Display result
                    document.getElementById('utmResult').textContent = utmUrl;
                    document.getElementById('resultSection').style.display = 'block';
                    document.getElementById('copyBtn').textContent = 'Copy URL';
                    document.getElementById('copyBtn').classList.remove('copied');

                    // Scroll to result
                    document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
                }

                function copyToClipboard() {
                    const utmUrl = document.getElementById('utmResult').textContent;
                    navigator.clipboard.writeText(utmUrl).then(() => {
                        const btn = document.getElementById('copyBtn');
                        btn.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                        btn.classList.add('copied');

                        setTimeout(() => {
                            btn.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy URL';
                            btn.classList.remove('copied');
                        }, 2000);
                    }).catch(() => {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = utmUrl;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);

                        const btn = document.getElementById('copyBtn');
                        btn.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                        btn.classList.add('copied');
                    });
                }

                // Allow Enter key to generate UTM
                document.getElementById('utmForm').addEventListener('keypress', function (e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        generateUTM();
                    }
                });
            </script>
</body>

</html>