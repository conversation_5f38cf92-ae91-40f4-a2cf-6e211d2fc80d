document.addEventListener('DOMContentLoaded', function () {
    const flag = document.getElementById('selected-flag');
    const languageMenu = document.getElementById('language-menu');

    // Toggle language menu on click
    flag.addEventListener('click', function () {
        languageMenu.style.display =
            languageMenu.style.display === 'block' ? 'none' : 'block';
    });

    // Close menu when clicking outside
    document.addEventListener('click', function (event) {
        if (!flag.contains(event.target) && !languageMenu.contains(event.target)) {
            languageMenu.style.display = 'none';
        }
    });
});
