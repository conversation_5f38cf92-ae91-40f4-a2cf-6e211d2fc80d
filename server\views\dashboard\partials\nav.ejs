<!-- Sidebar -->
<div id="sidebar"
    class="w-64 bg-white border-r border-gray-200 text-gray-700 h-screen fixed top-0 left-0 transform transition-transform duration-300 ease-in-out -translate-x-full md:translate-x-0 z-40">
    <div class="p-6">
        <h2 class="text-xl font-bold  text-gray-900">Dashboard</h2>
        <button id="closeSidebarBtn" class="md:hidden absolute top-6 right-6 text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    <nav class="mt-2 px-4">
        <ul class="space-y-1">
            <li>
                <a href="/dashboard" rel="noopener noreferrer"
                    class="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg <%= activeTab === 'overview' ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50' %>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    Overview
                </a>
            </li>
            <li>
                <a href="/dashboard/urls" rel="noopener noreferrer"
                    class="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg <%= activeTab === 'urls' ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50' %>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1">
                        </path>
                    </svg>
                    My URLs
                </a>
            </li>
            <li>
                <a href="/dashboard/analytics" rel="noopener noreferrer"
                    class="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg <%= activeTab === 'analytics' ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50' %>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                    Analytics
                </a>
            </li>
        </ul>
    </nav>
    <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <a href="/auth/logout" rel="noopener noreferrer"
            class="flex items-center px-4 py-2.5 text-sm font-medium text-gray-600 hover:bg-red-100 rounded-lg">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1">
                </path>
            </svg>
            Logout
        </a>
    </div>
</div>

<!-- Add overlay div right after the sidebar -->
<div id="sidebarOverlay"
    class="fixed inset-0 bg-black opacity-0 pointer-events-none transition-opacity duration-300 ease-in-out z-30">
</div>

<!-- Top Navbar -->
<nav class="bg-white border-b border-gray-200 fixed w-full z-30 h-16 transition-all duration-300">
    <div class="h-full px-4 md:px-6 flex items-center justify-between">
        <div class="flex items-center">
            <button id="openSidebarBtn" class="md:hidden p-2 text-gray-600 hover:bg-gray-100 rounded-lg">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 6h16M4 12h16M4 18h16">
                    </path>
                </svg>
            </button>
        </div>
        <div class="relative">
            <button id="userProfileBtn"
                class="flex items-center space-x-3 hover:bg-gray-50 rounded-lg px-3 py-1.5 border border-gray-200">
                <svg class="w-8 h-8 mr-3" fill="none" stroke="green" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span class="hidden md:inline-block text-sm font-medium  text-gray-700">
                    <%= user.email.split('@')[0] %>
                </span>
                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 9l-7 7-7-7" />
                </svg>
            </button>
            <div id="userMenu"
                class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 hidden">
                <div class="p-3 border-b border-gray-200">
                    <p class="text-sm font-medium text-gray-900">Signed in as</p>
                    <p class="text-sm text-gray-500">
                        <%= user.email %>
                    </p>
                </div>
                <div class="py-1">
                    <a href="/dashboard/profile" rel="noopener noreferrer"
                        class="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Profile
                    </a>
                    <a href="/dashboard/settings" rel="noopener noreferrer"
                        class="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Settings
                    </a>
                    <div class="border-t border-gray-200">
                        <a href="/auth/logout" rel="noopener noreferrer"
                            class="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-red-100">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>