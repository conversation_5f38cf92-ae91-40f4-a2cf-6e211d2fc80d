// server/redirect.js
const Url = require('../models/Url');
const FALLBACK_URL = '/';  // or your real homepage
async function redirect(shortId) {
  try {
    const url = await Url.findOne({ shortId });
    if (url) {
      // Increment the click count
      url.clickCount += 1;
      await url.save();

      return { status: 302, url: url.longUrl };
    } else {
      return { status: 302, url: FALLBACK_URL };
    }
  } catch (error) {
    return { status: 302, url: FALLBACK_URL };
  }
}

module.exports = { redirect };
